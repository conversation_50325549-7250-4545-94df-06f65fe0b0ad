import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PlayIcon,
  PauseIcon,
  SpeakerWaveIcon,
  DocumentDuplicateIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  ChevronUpIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { Transcription, TranscriptionSegment, Speaker } from '../../types';
import { formatTime } from '../../utils/dateUtils';
import toast from 'react-hot-toast';

interface TranscriptViewerProps {
  transcription: Transcription;
  audioUrl?: string;
  isPlaying?: boolean;
  currentTime?: number;
  onSeek?: (time: number) => void;
  onPlayPause?: () => void;
  onSegmentEdit?: (segmentId: string, newText: string) => void;
  onSpeakerEdit?: (speakerId: string, newName: string) => void;
  className?: string;
  showTimestamps?: boolean;
  showSpeakers?: boolean;
  showConfidence?: boolean;
  editable?: boolean;
}

const TranscriptViewer: React.FC<TranscriptViewerProps> = ({
  transcription,
  audioUrl,
  isPlaying = false,
  currentTime = 0,
  onSeek,
  onPlayPause,
  onSegmentEdit,
  onSpeakerEdit,
  className = '',
  showTimestamps = true,
  showSpeakers = true,
  showConfidence = false,
  editable = false
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [editingSegment, setEditingSegment] = useState<string | null>(null);
  const [editingText, setEditingText] = useState('');
  const [editingSpeaker, setEditingSpeaker] = useState<string | null>(null);
  const [editingSpeakerName, setEditingSpeakerName] = useState('');
  const [highlightedSegments, setHighlightedSegments] = useState<string[]>([]);
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const activeSegmentRef = useRef<HTMLDivElement>(null);

  // Find current active segment based on audio time
  const activeSegmentId = transcription.segments.find(
    segment => currentTime >= segment.startTime && currentTime <= segment.endTime
  )?.id;

  // Auto-scroll to active segment
  useEffect(() => {
    if (activeSegmentRef.current && activeSegmentId) {
      activeSegmentRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  }, [activeSegmentId]);

  // Search functionality
  useEffect(() => {
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      const matchingSegments = transcription.segments
        .filter(segment => segment.text.toLowerCase().includes(query))
        .map(segment => segment.id);
      setHighlightedSegments(matchingSegments);
    } else {
      setHighlightedSegments([]);
    }
  }, [searchQuery, transcription.segments]);

  const handleSegmentClick = useCallback((segment: TranscriptionSegment) => {
    if (onSeek) {
      onSeek(segment.startTime);
    }
  }, [onSeek]);

  const handleSegmentEdit = useCallback((segmentId: string, text: string) => {
    setEditingSegment(segmentId);
    setEditingText(text);
  }, []);

  const handleSaveSegmentEdit = useCallback(() => {
    if (editingSegment && onSegmentEdit) {
      onSegmentEdit(editingSegment, editingText);
      setEditingSegment(null);
      setEditingText('');
      toast.success('Segment updated successfully');
    }
  }, [editingSegment, editingText, onSegmentEdit]);

  const handleCancelSegmentEdit = useCallback(() => {
    setEditingSegment(null);
    setEditingText('');
  }, []);

  const handleSpeakerEdit = useCallback((speakerId: string, currentName: string) => {
    setEditingSpeaker(speakerId);
    setEditingSpeakerName(currentName || `Speaker ${speakerId}`);
  }, []);

  const handleSaveSpeakerEdit = useCallback(() => {
    if (editingSpeaker && onSpeakerEdit) {
      onSpeakerEdit(editingSpeaker, editingSpeakerName);
      setEditingSpeaker(null);
      setEditingSpeakerName('');
      toast.success('Speaker name updated successfully');
    }
  }, [editingSpeaker, editingSpeakerName, onSpeakerEdit]);

  const handleCancelSpeakerEdit = useCallback(() => {
    setEditingSpeaker(null);
    setEditingSpeakerName('');
  }, []);

  const copyToClipboard = useCallback(() => {
    const text = transcription.segments
      .map(segment => {
        const speaker = transcription.speakers.find(s => s.id === segment.speakerId);
        const speakerName = speaker?.name || `Speaker ${segment.speakerId}`;
        const timestamp = showTimestamps ? `[${formatTime(segment.startTime)}] ` : '';
        const speakerPrefix = showSpeakers && segment.speakerId ? `${speakerName}: ` : '';
        return `${timestamp}${speakerPrefix}${segment.text}`;
      })
      .join('\n');

    navigator.clipboard.writeText(text).then(() => {
      toast.success('Transcript copied to clipboard');
    }).catch(() => {
      toast.error('Failed to copy transcript');
    });
  }, [transcription, showTimestamps, showSpeakers]);

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'text-green-600 dark:text-green-400';
    if (confidence >= 0.7) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const highlightSearchText = (text: string, query: string) => {
    if (!query.trim()) return text;
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">
          {part}
        </mark>
      ) : part
    );
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Transcript
          </h3>
          
          {transcription.language && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
              {transcription.language.toUpperCase()}
            </span>
          )}
          
          {transcription.confidence && (
            <span className={`text-sm font-medium ${getConfidenceColor(transcription.confidence)}`}>
              {Math.round(transcription.confidence * 100)}% confidence
            </span>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* Search */}
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search transcript..."
              className="pl-9 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Copy button */}
          <button
            onClick={copyToClipboard}
            className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            title="Copy transcript"
          >
            <DocumentDuplicateIcon className="w-5 h-5" />
          </button>

          {/* Collapse toggle */}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            title={isCollapsed ? 'Expand transcript' : 'Collapse transcript'}
          >
            {isCollapsed ? (
              <ChevronDownIcon className="w-5 h-5" />
            ) : (
              <ChevronUpIcon className="w-5 h-5" />
            )}
          </button>
        </div>
      </div>

      <AnimatePresence>
        {!isCollapsed && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            {/* Speakers list */}
            {showSpeakers && transcription.speakers.length > 0 && (
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  Speakers ({transcription.speakers.length})
                </h4>
                <div className="flex flex-wrap gap-2">
                  {transcription.speakers.map((speaker) => (
                    <div
                      key={speaker.id}
                      className="flex items-center space-x-2 px-3 py-1.5 bg-gray-100 dark:bg-gray-700 rounded-full"
                    >
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: speaker.color }}
                      />
                      {editingSpeaker === speaker.id ? (
                        <div className="flex items-center space-x-1">
                          <input
                            type="text"
                            value={editingSpeakerName}
                            onChange={(e) => setEditingSpeakerName(e.target.value)}
                            className="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') handleSaveSpeakerEdit();
                              if (e.key === 'Escape') handleCancelSpeakerEdit();
                            }}
                            autoFocus
                          />
                          <button
                            onClick={handleSaveSpeakerEdit}
                            className="p-1 text-green-600 hover:text-green-700"
                          >
                            <CheckIcon className="w-3 h-3" />
                          </button>
                          <button
                            onClick={handleCancelSpeakerEdit}
                            className="p-1 text-red-600 hover:text-red-700"
                          >
                            <XMarkIcon className="w-3 h-3" />
                          </button>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-1">
                          <span className="text-sm text-gray-700 dark:text-gray-300">
                            {speaker.name || `Speaker ${speaker.id}`}
                          </span>
                          {editable && (
                            <button
                              onClick={() => handleSpeakerEdit(speaker.id, speaker.name || '')}
                              className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                            >
                              <PencilIcon className="w-3 h-3" />
                            </button>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Transcript content */}
            <div
              ref={containerRef}
              className="p-4 max-h-96 overflow-y-auto space-y-4"
            >
              {transcription.segments.map((segment) => {
                const speaker = transcription.speakers.find(s => s.id === segment.speakerId);
                const isActive = segment.id === activeSegmentId;
                const isHighlighted = highlightedSegments.includes(segment.id);
                const isEditing = editingSegment === segment.id;

                return (
                  <motion.div
                    key={segment.id}
                    ref={isActive ? activeSegmentRef : undefined}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`group p-3 rounded-lg border transition-all duration-200 cursor-pointer ${
                      isActive
                        ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700'
                        : isHighlighted
                        ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-700'
                        : 'bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                    onClick={() => !isEditing && handleSegmentClick(segment)}
                  >
                    <div className="flex items-start space-x-3">
                      {/* Timestamp and controls */}
                      <div className="flex-shrink-0 flex items-center space-x-2">
                        {showTimestamps && (
                          <span className="text-xs font-mono text-gray-500 dark:text-gray-400 min-w-[60px]">
                            {formatTime(segment.startTime)}
                          </span>
                        )}
                        
                        {audioUrl && onPlayPause && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              if (!isActive) {
                                handleSegmentClick(segment);
                              }
                              onPlayPause();
                            }}
                            className="p-1 text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            {isPlaying && isActive ? (
                              <PauseIcon className="w-4 h-4" />
                            ) : (
                              <PlayIcon className="w-4 h-4" />
                            )}
                          </button>
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        {/* Speaker */}
                        {showSpeakers && speaker && (
                          <div className="flex items-center space-x-2 mb-1">
                            <div
                              className="w-2 h-2 rounded-full"
                              style={{ backgroundColor: speaker.color }}
                            />
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              {speaker.name || `Speaker ${speaker.id}`}
                            </span>
                          </div>
                        )}

                        {/* Text */}
                        {isEditing ? (
                          <div className="space-y-2">
                            <textarea
                              value={editingText}
                              onChange={(e) => setEditingText(e.target.value)}
                              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none"
                              rows={3}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' && e.ctrlKey) {
                                  handleSaveSegmentEdit();
                                }
                                if (e.key === 'Escape') {
                                  handleCancelSegmentEdit();
                                }
                              }}
                              autoFocus
                            />
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={handleSaveSegmentEdit}
                                className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                              >
                                Save
                              </button>
                              <button
                                onClick={handleCancelSegmentEdit}
                                className="px-3 py-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
                              >
                                Cancel
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-start justify-between">
                            <p className="text-gray-900 dark:text-white leading-relaxed">
                              {highlightSearchText(segment.text, searchQuery)}
                            </p>
                            
                            <div className="flex items-center space-x-1 ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                              {showConfidence && (
                                <span className={`text-xs ${getConfidenceColor(segment.confidence)}`}>
                                  {Math.round(segment.confidence * 100)}%
                                </span>
                              )}
                              
                              {editable && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleSegmentEdit(segment.id, segment.text);
                                  }}
                                  className="p-1 text-gray-500 hover:text-blue-600 dark:hover:text-blue-400"
                                  title="Edit segment"
                                >
                                  <PencilIcon className="w-3 h-3" />
                                </button>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default TranscriptViewer;
