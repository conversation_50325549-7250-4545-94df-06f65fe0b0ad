import React, { useRef, useEffect, useState, useCallback } from 'react';
import { motion } from 'framer-motion';

interface WaveformVisualizerProps {
  audioLevel: number;
  isRecording: boolean;
  audioBlob?: Blob;
  duration: number;
  className?: string;
  height?: number;
  barCount?: number;
  barWidth?: number;
  barGap?: number;
  color?: string;
  backgroundColor?: string;
}

const WaveformVisualizer: React.FC<WaveformVisualizerProps> = ({
  audioLevel,
  isRecording,
  audioBlob,
  duration,
  className = '',
  height = 120,
  barCount = 50,
  barWidth = 4,
  barGap = 2,
  color = '#3b82f6',
  backgroundColor = '#f1f5f9'
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const [waveformData, setWaveformData] = useState<number[]>([]);
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef<HTMLAudioElement>();

  // Generate live waveform data during recording
  const generateLiveWaveform = useCallback(() => {
    if (!isRecording) return;

    setWaveformData(prev => {
      const newData = [...prev];
      
      // Add new bar based on audio level
      const normalizedLevel = Math.min(audioLevel * 100, 100);
      newData.push(normalizedLevel);
      
      // Keep only the last barCount bars
      if (newData.length > barCount) {
        newData.shift();
      }
      
      return newData;
    });
  }, [audioLevel, isRecording, barCount]);

  // Generate waveform from audio blob
  const generateWaveformFromBlob = useCallback(async (blob: Blob) => {
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const arrayBuffer = await blob.arrayBuffer();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      
      const channelData = audioBuffer.getChannelData(0);
      const samples = barCount;
      const blockSize = Math.floor(channelData.length / samples);
      const waveform: number[] = [];
      
      for (let i = 0; i < samples; i++) {
        let sum = 0;
        for (let j = 0; j < blockSize; j++) {
          sum += Math.abs(channelData[i * blockSize + j]);
        }
        waveform.push((sum / blockSize) * 100);
      }
      
      setWaveformData(waveform);
      
      // Create audio element for playback
      const audioUrl = URL.createObjectURL(blob);
      audioRef.current = new Audio(audioUrl);
      audioRef.current.addEventListener('timeupdate', () => {
        if (audioRef.current) {
          setCurrentTime(audioRef.current.currentTime);
        }
      });
      audioRef.current.addEventListener('ended', () => {
        setIsPlaying(false);
        setCurrentTime(0);
      });
      
    } catch (error) {
      console.error('Error generating waveform:', error);
    }
  }, [barCount]);

  // Draw waveform on canvas
  const drawWaveform = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height: canvasHeight } = canvas;
    
    // Clear canvas
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, width, canvasHeight);
    
    if (waveformData.length === 0) {
      // Draw placeholder bars
      ctx.fillStyle = '#e2e8f0';
      for (let i = 0; i < barCount; i++) {
        const x = i * (barWidth + barGap);
        const barHeight = 4;
        const y = (canvasHeight - barHeight) / 2;
        ctx.fillRect(x, y, barWidth, barHeight);
      }
      return;
    }

    // Calculate progress for playback visualization
    const progress = audioRef.current ? currentTime / (audioRef.current.duration || 1) : 0;
    const progressBarIndex = Math.floor(progress * waveformData.length);

    // Draw waveform bars
    waveformData.forEach((amplitude, index) => {
      const x = index * (barWidth + barGap);
      const normalizedAmplitude = Math.max(amplitude / 100, 0.02); // Minimum height
      const barHeight = normalizedAmplitude * (canvasHeight - 20);
      const y = (canvasHeight - barHeight) / 2;
      
      // Color based on playback progress or recording state
      let barColor = color;
      if (audioRef.current && !isRecording) {
        barColor = index <= progressBarIndex ? color : '#cbd5e1';
      } else if (isRecording && index === waveformData.length - 1) {
        barColor = '#ef4444'; // Red for current recording bar
      }
      
      ctx.fillStyle = barColor;
      ctx.fillRect(x, y, barWidth, barHeight);
      
      // Add glow effect for recording
      if (isRecording && index >= waveformData.length - 3) {
        ctx.shadowColor = barColor;
        ctx.shadowBlur = 10;
        ctx.fillRect(x, y, barWidth, barHeight);
        ctx.shadowBlur = 0;
      }
    });

    // Draw time indicator
    if (audioRef.current && !isRecording) {
      const indicatorX = progressBarIndex * (barWidth + barGap) + barWidth / 2;
      ctx.strokeStyle = '#1f2937';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(indicatorX, 0);
      ctx.lineTo(indicatorX, canvasHeight);
      ctx.stroke();
    }
  }, [waveformData, currentTime, isRecording, barCount, barWidth, barGap, color, backgroundColor, height]);

  // Animation loop for live recording
  useEffect(() => {
    if (isRecording) {
      const animate = () => {
        generateLiveWaveform();
        drawWaveform();
        animationRef.current = requestAnimationFrame(animate);
      };
      animationRef.current = requestAnimationFrame(animate);
    } else {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      drawWaveform();
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isRecording, generateLiveWaveform, drawWaveform]);

  // Generate waveform when audio blob changes
  useEffect(() => {
    if (audioBlob && !isRecording) {
      generateWaveformFromBlob(audioBlob);
    }
  }, [audioBlob, isRecording, generateWaveformFromBlob]);

  // Handle canvas click for seeking
  const handleCanvasClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!audioRef.current || isRecording) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const progress = x / canvas.width;
    const newTime = progress * audioRef.current.duration;
    
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  }, [isRecording]);

  // Play/pause functionality
  const togglePlayback = useCallback(() => {
    if (!audioRef.current || isRecording) return;

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  }, [isPlaying, isRecording]);

  // Format time display
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`waveform-container ${className}`}>
      {/* Canvas */}
      <canvas
        ref={canvasRef}
        width={barCount * (barWidth + barGap)}
        height={height}
        className="w-full cursor-pointer rounded-lg"
        onClick={handleCanvasClick}
        style={{ maxWidth: '100%' }}
      />
      
      {/* Controls and Info */}
      <div className="flex items-center justify-between mt-4">
        <div className="flex items-center space-x-4">
          {/* Play/Pause Button */}
          {audioBlob && !isRecording && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={togglePlayback}
              className="flex items-center justify-center w-10 h-10 bg-blue-500 hover:bg-blue-600 text-white rounded-full transition-colors"
            >
              {isPlaying ? (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="w-5 h-5 ml-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
              )}
            </motion.button>
          )}
          
          {/* Time Display */}
          <div className="text-sm font-mono text-gray-600 dark:text-gray-400">
            {audioRef.current ? (
              <>
                {formatTime(currentTime)} / {formatTime(audioRef.current.duration || 0)}
              </>
            ) : (
              formatTime(duration)
            )}
          </div>
        </div>
        
        {/* Recording Indicator */}
        {isRecording && (
          <motion.div
            animate={{ opacity: [1, 0.5, 1] }}
            transition={{ duration: 1, repeat: Infinity }}
            className="flex items-center space-x-2 text-red-500"
          >
            <div className="w-3 h-3 bg-red-500 rounded-full" />
            <span className="text-sm font-medium">Recording</span>
          </motion.div>
        )}
        
        {/* Audio Level Indicator */}
        {isRecording && (
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500">Level:</span>
            <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-green-400 via-yellow-400 to-red-500"
                style={{ width: `${Math.min(audioLevel * 100, 100)}%` }}
                transition={{ duration: 0.1 }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WaveformVisualizer;
