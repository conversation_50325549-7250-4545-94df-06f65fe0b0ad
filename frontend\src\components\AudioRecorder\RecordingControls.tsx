import React from 'react';
import { motion } from 'framer-motion';
import {
  MicrophoneIcon,
  StopIcon,
  PauseIcon,
  PlayIcon,
  TrashIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import { RecordingSession } from '../../types';

interface RecordingControlsProps {
  status: RecordingSession['status'];
  onStart: () => void;
  onStop: () => void;
  onPause: () => void;
  onClear: () => void;
  onDownload?: () => void;
  disabled?: boolean;
  className?: string;
}

const RecordingControls: React.FC<RecordingControlsProps> = ({
  status,
  onStart,
  onStop,
  onPause,
  onClear,
  onDownload,
  disabled = false,
  className = ''
}) => {
  const buttonBaseClasses = "flex items-center justify-center w-12 h-12 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed";
  
  const getButtonVariants = (variant: 'primary' | 'secondary' | 'danger') => {
    switch (variant) {
      case 'primary':
        return "bg-blue-500 hover:bg-blue-600 text-white focus:ring-blue-500 shadow-lg hover:shadow-xl";
      case 'secondary':
        return "bg-gray-200 hover:bg-gray-300 text-gray-700 focus:ring-gray-500 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300";
      case 'danger':
        return "bg-red-500 hover:bg-red-600 text-white focus:ring-red-500 shadow-lg hover:shadow-xl";
      default:
        return "";
    }
  };

  const renderStartButton = () => (
    <motion.button
      whileHover={{ scale: disabled ? 1 : 1.05 }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      onClick={onStart}
      disabled={disabled}
      className={`${buttonBaseClasses} ${getButtonVariants('primary')} w-16 h-16`}
      title="Start Recording"
    >
      <MicrophoneIcon className="w-8 h-8" />
    </motion.button>
  );

  const renderRecordingButton = () => (
    <motion.button
      animate={{ 
        scale: [1, 1.1, 1],
        boxShadow: [
          "0 0 0 0 rgba(239, 68, 68, 0.7)",
          "0 0 0 10px rgba(239, 68, 68, 0)",
          "0 0 0 0 rgba(239, 68, 68, 0)"
        ]
      }}
      transition={{ duration: 1.5, repeat: Infinity }}
      onClick={onStop}
      disabled={disabled}
      className={`${buttonBaseClasses} ${getButtonVariants('danger')} w-16 h-16`}
      title="Stop Recording"
    >
      <StopIcon className="w-8 h-8" />
    </motion.button>
  );

  const renderPauseButton = () => (
    <motion.button
      whileHover={{ scale: disabled ? 1 : 1.05 }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      onClick={onPause}
      disabled={disabled}
      className={`${buttonBaseClasses} ${getButtonVariants('secondary')}`}
      title={status === 'paused' ? 'Resume Recording' : 'Pause Recording'}
    >
      {status === 'paused' ? (
        <PlayIcon className="w-6 h-6" />
      ) : (
        <PauseIcon className="w-6 h-6" />
      )}
    </motion.button>
  );

  const renderClearButton = () => (
    <motion.button
      whileHover={{ scale: disabled ? 1 : 1.05 }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      onClick={onClear}
      disabled={disabled}
      className={`${buttonBaseClasses} ${getButtonVariants('secondary')}`}
      title="Clear Recording"
    >
      <TrashIcon className="w-6 h-6" />
    </motion.button>
  );

  const renderDownloadButton = () => (
    <motion.button
      whileHover={{ scale: disabled ? 1 : 1.05 }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      onClick={onDownload}
      disabled={disabled || !onDownload}
      className={`${buttonBaseClasses} ${getButtonVariants('secondary')}`}
      title="Download Recording"
    >
      <ArrowDownTrayIcon className="w-6 h-6" />
    </motion.button>
  );

  const getStatusText = () => {
    switch (status) {
      case 'idle':
        return 'Ready to record';
      case 'recording':
        return 'Recording in progress...';
      case 'paused':
        return 'Recording paused';
      case 'stopped':
        return 'Recording completed';
      case 'processing':
        return 'Processing audio...';
      default:
        return '';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'idle':
        return 'text-gray-500 dark:text-gray-400';
      case 'recording':
        return 'text-red-500';
      case 'paused':
        return 'text-yellow-500';
      case 'stopped':
        return 'text-green-500';
      case 'processing':
        return 'text-blue-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className={`flex flex-col items-center space-y-6 ${className}`}>
      {/* Status Text */}
      <div className="text-center">
        <p className={`text-sm font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </p>
      </div>

      {/* Main Control Button */}
      <div className="flex items-center justify-center">
        {status === 'idle' && renderStartButton()}
        {(status === 'recording' || status === 'paused') && renderRecordingButton()}
        {status === 'stopped' && renderStartButton()}
        {status === 'processing' && (
          <div className="w-16 h-16 flex items-center justify-center">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"
            />
          </div>
        )}
      </div>

      {/* Secondary Controls */}
      <div className="flex items-center space-x-4">
        {/* Pause/Resume Button */}
        {(status === 'recording' || status === 'paused') && renderPauseButton()}
        
        {/* Clear Button */}
        {(status === 'stopped' || status === 'paused') && renderClearButton()}
        
        {/* Download Button */}
        {status === 'stopped' && onDownload && renderDownloadButton()}
      </div>

      {/* Keyboard Shortcuts Info */}
      <div className="text-xs text-gray-400 dark:text-gray-500 text-center space-y-1">
        <p>Keyboard shortcuts:</p>
        <div className="flex flex-wrap justify-center gap-x-4 gap-y-1">
          <span><kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-800 rounded text-xs">Space</kbd> Start/Stop</span>
          <span><kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-800 rounded text-xs">P</kbd> Pause</span>
          <span><kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-800 rounded text-xs">C</kbd> Clear</span>
        </div>
      </div>
    </div>
  );
};

// Keyboard shortcuts hook
export const useRecordingKeyboardShortcuts = (
  status: RecordingSession['status'],
  onStart: () => void,
  onStop: () => void,
  onPause: () => void,
  onClear: () => void
) => {
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ignore if user is typing in an input field
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement
      ) {
        return;
      }

      switch (event.code) {
        case 'Space':
          event.preventDefault();
          if (status === 'idle' || status === 'stopped') {
            onStart();
          } else if (status === 'recording' || status === 'paused') {
            onStop();
          }
          break;
        
        case 'KeyP':
          event.preventDefault();
          if (status === 'recording' || status === 'paused') {
            onPause();
          }
          break;
        
        case 'KeyC':
          event.preventDefault();
          if (status === 'stopped' || status === 'paused') {
            onClear();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [status, onStart, onStop, onPause, onClear]);
};

export default RecordingControls;
