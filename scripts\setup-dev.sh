#!/bin/bash

# QuickCapt Development Environment Setup Script
# This script sets up the complete development environment for QuickCapt

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."
    
    # Check Node.js
    if ! command_exists node; then
        log_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js version 18+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check npm
    if ! command_exists npm; then
        log_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    # Check Docker (optional)
    if command_exists docker; then
        log_success "Docker found: $(docker --version)"
    else
        log_warning "Docker not found. Docker is optional but recommended for full development setup."
    fi
    
    # Check Docker Compose (optional)
    if command_exists docker-compose; then
        log_success "Docker Compose found: $(docker-compose --version)"
    else
        log_warning "Docker Compose not found. Required for containerized development."
    fi
    
    # Check PostgreSQL (optional)
    if command_exists psql; then
        log_success "PostgreSQL client found: $(psql --version)"
    else
        log_warning "PostgreSQL client not found. Will use Docker for database."
    fi
    
    # Check Redis (optional)
    if command_exists redis-cli; then
        log_success "Redis CLI found"
    else
        log_warning "Redis CLI not found. Will use Docker for Redis."
    fi
    
    log_success "System requirements check completed"
}

# Setup environment variables
setup_environment() {
    log_info "Setting up environment variables..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            log_success "Created .env file from .env.example"
        else
            log_error ".env.example file not found"
            exit 1
        fi
    else
        log_warning ".env file already exists, skipping creation"
    fi
    
    # Prompt for OpenAI API key if not set
    if ! grep -q "OPENAI_API_KEY=sk-" .env; then
        echo
        log_warning "OpenAI API key not found in .env file"
        echo "Please enter your OpenAI API key (or press Enter to skip):"
        read -r OPENAI_KEY
        if [ -n "$OPENAI_KEY" ]; then
            sed -i.bak "s/OPENAI_API_KEY=.*/OPENAI_API_KEY=$OPENAI_KEY/" .env
            log_success "OpenAI API key updated in .env file"
        fi
    fi
    
    log_success "Environment setup completed"
}

# Install dependencies
install_dependencies() {
    log_info "Installing project dependencies..."
    
    # Install root dependencies
    log_info "Installing root dependencies..."
    npm install
    
    # Install frontend dependencies
    log_info "Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
    
    # Install backend dependencies
    log_info "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    
    # Install AI services dependencies
    log_info "Installing AI services dependencies..."
    cd ai-services
    npm install
    cd ..
    
    log_success "All dependencies installed successfully"
}

# Setup database
setup_database() {
    log_info "Setting up database..."
    
    if command_exists docker && command_exists docker-compose; then
        log_info "Starting database services with Docker..."
        docker-compose up -d postgres redis elasticsearch
        
        # Wait for services to be ready
        log_info "Waiting for database services to be ready..."
        sleep 10
        
        # Check if PostgreSQL is ready
        for i in {1..30}; do
            if docker-compose exec -T postgres pg_isready -U quickcapt -d quickcapt >/dev/null 2>&1; then
                log_success "PostgreSQL is ready"
                break
            fi
            if [ $i -eq 30 ]; then
                log_error "PostgreSQL failed to start"
                exit 1
            fi
            sleep 2
        done
        
        # Run database migrations
        log_info "Running database migrations..."
        cd backend
        npm run db:generate
        npm run db:migrate
        cd ..
        
    else
        log_warning "Docker not available. Please set up PostgreSQL, Redis, and Elasticsearch manually."
        log_info "Database connection details:"
        echo "  PostgreSQL: postgresql://quickcapt:password@localhost:5432/quickcapt"
        echo "  Redis: redis://localhost:6379"
        echo "  Elasticsearch: http://localhost:9200"
    fi
    
    log_success "Database setup completed"
}

# Create necessary directories
create_directories() {
    log_info "Creating necessary directories..."
    
    mkdir -p uploads
    mkdir -p logs
    mkdir -p temp
    mkdir -p backend/uploads
    mkdir -p backend/logs
    mkdir -p ai-services/temp
    mkdir -p ai-services/models
    
    log_success "Directories created successfully"
}

# Setup Git hooks (optional)
setup_git_hooks() {
    log_info "Setting up Git hooks..."
    
    if [ -d .git ]; then
        # Pre-commit hook for linting
        cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
echo "Running pre-commit checks..."

# Run linting
npm run lint:frontend
npm run lint:backend

# Run tests
npm run test:frontend
npm run test:backend

echo "Pre-commit checks passed!"
EOF
        
        chmod +x .git/hooks/pre-commit
        log_success "Git hooks setup completed"
    else
        log_warning "Not a Git repository, skipping Git hooks setup"
    fi
}

# Verify installation
verify_installation() {
    log_info "Verifying installation..."
    
    # Check if all package.json files exist
    for dir in . frontend backend ai-services; do
        if [ ! -f "$dir/package.json" ]; then
            log_error "package.json not found in $dir"
            exit 1
        fi
    done
    
    # Check if .env file exists
    if [ ! -f .env ]; then
        log_error ".env file not found"
        exit 1
    fi
    
    # Check if node_modules exist
    for dir in . frontend backend ai-services; do
        if [ ! -d "$dir/node_modules" ]; then
            log_error "node_modules not found in $dir"
            exit 1
        fi
    done
    
    log_success "Installation verification completed"
}

# Print next steps
print_next_steps() {
    echo
    log_success "🎉 QuickCapt development environment setup completed!"
    echo
    echo "Next steps:"
    echo "1. Review and update the .env file with your configuration"
    echo "2. Start the development servers:"
    echo "   npm run dev"
    echo
    echo "3. Or start individual services:"
    echo "   Frontend:    cd frontend && npm start"
    echo "   Backend:     cd backend && npm run dev"
    echo "   AI Services: cd ai-services && npm run dev"
    echo
    echo "4. Access the application:"
    echo "   Frontend:  http://localhost:3000"
    echo "   Backend:   http://localhost:3001"
    echo "   AI API:    http://localhost:3002"
    echo
    echo "5. With Docker (recommended):"
    echo "   docker-compose up -d"
    echo
    echo "For more information, see the documentation in the docs/ directory."
    echo
}

# Main execution
main() {
    echo
    log_info "🚀 Starting QuickCapt development environment setup..."
    echo
    
    check_requirements
    setup_environment
    create_directories
    install_dependencies
    setup_database
    setup_git_hooks
    verify_installation
    print_next_steps
}

# Run main function
main "$@"
