# Prometheus configuration for QuickCapt monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'quickcapt'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # Node Exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # QuickCapt Backend API
  - job_name: 'quickcapt-backend'
    static_configs:
      - targets: ['backend:3001']
    metrics_path: /metrics
    scrape_interval: 10s
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']

  # QuickCapt AI Services
  - job_name: 'quickcapt-ai-services'
    static_configs:
      - targets: ['ai-services:3002']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 5s

  # PostgreSQL Database
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis Cache
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Elasticsearch
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch-exporter:9114']
    scrape_interval: 30s

  # Nginx (if used as reverse proxy)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s

  # Docker containers
  - job_name: 'docker'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

  # Blackbox exporter for external monitoring
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://quickcapt.com
        - https://api.quickcapt.com/health
        - https://ai.quickcapt.com/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # Application-specific metrics
  - job_name: 'quickcapt-custom-metrics'
    static_configs:
      - targets: ['backend:3001']
    metrics_path: /api/metrics
    scrape_interval: 30s
    basic_auth:
      username: 'metrics'
      password: 'metrics_password'

# Remote write configuration (for long-term storage)
remote_write:
  - url: "https://prometheus-remote-write.example.com/api/v1/write"
    basic_auth:
      username: "remote_write_user"
      password: "remote_write_password"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# Remote read configuration
remote_read:
  - url: "https://prometheus-remote-read.example.com/api/v1/read"
    basic_auth:
      username: "remote_read_user"
      password: "remote_read_password"

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 50GB
    wal-compression: true

# Web configuration
web:
  console.templates: '/etc/prometheus/consoles'
  console.libraries: '/etc/prometheus/console_libraries'
  enable-lifecycle: true
  enable-admin-api: true
