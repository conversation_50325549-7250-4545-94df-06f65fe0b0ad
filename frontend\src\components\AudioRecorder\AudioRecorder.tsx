import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MicrophoneIcon, 
  StopIcon, 
  PauseIcon, 
  PlayIcon,
  TrashIcon,
  DocumentArrowUpIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

import WaveformVisualizer from './WaveformVisualizer';
import RecordingControls from './RecordingControls';
import { useAudioRecording } from '../../hooks/useAudioRecording';
import { useWebSocket } from '../../hooks/useWebSocket';
import { RecordingSession, RecordingSettings } from '../../types';

interface AudioRecorderProps {
  onRecordingComplete?: (audioBlob: Blob, duration: number) => void;
  onTranscriptionUpdate?: (text: string, isFinal: boolean) => void;
  settings?: Partial<RecordingSettings>;
  className?: string;
}

const AudioRecorder: React.FC<AudioRecorderProps> = ({
  onRecordingComplete,
  onTranscriptionUpdate,
  settings = {},
  className = ''
}) => {
  const [session, setSession] = useState<RecordingSession>({
    id: '',
    status: 'idle',
    duration: 0,
    liveTranscription: '',
    settings: {
      quality: 'high',
      format: 'webm',
      sampleRate: 44100,
      channels: 1,
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true,
      ...settings
    }
  });

  const {
    isRecording,
    isPaused,
    audioBlob,
    duration,
    audioLevel,
    startRecording,
    pauseRecording,
    resumeRecording,
    stopRecording,
    clearRecording,
    error: recordingError
  } = useAudioRecording(session.settings);

  const { sendMessage, lastMessage } = useWebSocket('/api/ws/transcription');

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Update session status based on recording state
  useEffect(() => {
    let status: RecordingSession['status'] = 'idle';
    if (isRecording && !isPaused) status = 'recording';
    else if (isRecording && isPaused) status = 'paused';
    else if (audioBlob) status = 'stopped';

    setSession(prev => ({
      ...prev,
      status,
      duration,
      audioBlob
    }));
  }, [isRecording, isPaused, audioBlob, duration]);

  // Handle WebSocket transcription updates
  useEffect(() => {
    if (lastMessage?.type === 'transcription_update') {
      const { text, isFinal } = lastMessage.payload;
      setSession(prev => ({
        ...prev,
        liveTranscription: prev.liveTranscription + (isFinal ? text + ' ' : text)
      }));
      onTranscriptionUpdate?.(text, isFinal);
    }
  }, [lastMessage, onTranscriptionUpdate]);

  // Handle recording errors
  useEffect(() => {
    if (recordingError) {
      toast.error(`Recording error: ${recordingError}`);
    }
  }, [recordingError]);

  const handleStartRecording = useCallback(async () => {
    try {
      const sessionId = `session_${Date.now()}`;
      setSession(prev => ({
        ...prev,
        id: sessionId,
        startTime: Date.now(),
        liveTranscription: ''
      }));

      await startRecording();
      
      // Initialize WebSocket session for live transcription
      sendMessage({
        type: 'start_session',
        payload: { sessionId, settings: session.settings }
      });

      toast.success('Recording started');
    } catch (error) {
      toast.error('Failed to start recording');
      console.error('Recording start error:', error);
    }
  }, [startRecording, sendMessage, session.settings]);

  const handleStopRecording = useCallback(async () => {
    try {
      const blob = await stopRecording();
      if (blob) {
        setSession(prev => ({
          ...prev,
          endTime: Date.now(),
          audioBlob: blob
        }));

        // End WebSocket session
        sendMessage({
          type: 'end_session',
          payload: { sessionId: session.id }
        });

        onRecordingComplete?.(blob, duration);
        toast.success('Recording completed');
      }
    } catch (error) {
      toast.error('Failed to stop recording');
      console.error('Recording stop error:', error);
    }
  }, [stopRecording, sendMessage, session.id, duration, onRecordingComplete]);

  const handlePauseRecording = useCallback(() => {
    if (isPaused) {
      resumeRecording();
      toast.success('Recording resumed');
    } else {
      pauseRecording();
      toast.success('Recording paused');
    }
  }, [isPaused, pauseRecording, resumeRecording]);

  const handleClearRecording = useCallback(() => {
    clearRecording();
    setSession(prev => ({
      ...prev,
      id: '',
      status: 'idle',
      duration: 0,
      audioBlob: undefined,
      liveTranscription: '',
      startTime: undefined,
      endTime: undefined
    }));
    toast.success('Recording cleared');
  }, [clearRecording]);

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const validTypes = ['audio/wav', 'audio/mp3', 'audio/m4a', 'audio/webm', 'audio/ogg'];
      if (!validTypes.includes(file.type)) {
        toast.error('Please select a valid audio file (WAV, MP3, M4A, WebM, OGG)');
        return;
      }

      // Validate file size (max 100MB)
      const maxSize = 100 * 1024 * 1024;
      if (file.size > maxSize) {
        toast.error('File size must be less than 100MB');
        return;
      }

      // Convert file to blob and process
      const blob = new Blob([file], { type: file.type });
      setSession(prev => ({
        ...prev,
        id: `upload_${Date.now()}`,
        status: 'stopped',
        audioBlob: blob,
        duration: 0 // Will be calculated after processing
      }));

      onRecordingComplete?.(blob, 0);
      toast.success('Audio file uploaded successfully');
    }
  }, [onRecordingComplete]);

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Audio Recorder
        </h2>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {formatDuration(duration)}
          </span>
          {session.status === 'recording' && (
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1, repeat: Infinity }}
              className="w-3 h-3 bg-red-500 rounded-full"
            />
          )}
        </div>
      </div>

      {/* Waveform Visualizer */}
      <div className="mb-6">
        <WaveformVisualizer
          audioLevel={audioLevel}
          isRecording={session.status === 'recording'}
          audioBlob={session.audioBlob}
          duration={duration}
        />
      </div>

      {/* Recording Controls */}
      <RecordingControls
        status={session.status}
        onStart={handleStartRecording}
        onStop={handleStopRecording}
        onPause={handlePauseRecording}
        onClear={handleClearRecording}
        disabled={!!recordingError}
      />

      {/* File Upload */}
      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-center">
          <button
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            <DocumentArrowUpIcon className="w-5 h-5" />
            <span>Upload Audio File</span>
          </button>
          <input
            ref={fileInputRef}
            type="file"
            accept="audio/*"
            onChange={handleFileUpload}
            className="hidden"
          />
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">
          Supported formats: WAV, MP3, M4A, WebM, OGG (max 100MB)
        </p>
      </div>

      {/* Live Transcription */}
      <AnimatePresence>
        {session.liveTranscription && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Live Transcription
            </h3>
            <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 max-h-40 overflow-y-auto">
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                {session.liveTranscription}
                {session.status === 'recording' && (
                  <motion.span
                    animate={{ opacity: [1, 0, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                    className="inline-block w-2 h-5 bg-blue-500 ml-1"
                  />
                )}
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Recording Info */}
      {session.status !== 'idle' && (
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500 dark:text-gray-400">Quality:</span>
              <span className="ml-2 text-gray-900 dark:text-white capitalize">
                {session.settings.quality}
              </span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Format:</span>
              <span className="ml-2 text-gray-900 dark:text-white uppercase">
                {session.settings.format}
              </span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Sample Rate:</span>
              <span className="ml-2 text-gray-900 dark:text-white">
                {session.settings.sampleRate} Hz
              </span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Channels:</span>
              <span className="ml-2 text-gray-900 dark:text-white">
                {session.settings.channels === 1 ? 'Mono' : 'Stereo'}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AudioRecorder;
