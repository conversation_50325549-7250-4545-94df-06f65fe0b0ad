import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import os from 'os';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

export interface SystemMetrics {
  timestamp: Date;
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  network: {
    connections: number;
    activeUsers: number;
  };
}

export interface ApplicationMetrics {
  timestamp: Date;
  requests: {
    total: number;
    successful: number;
    failed: number;
    averageResponseTime: number;
  };
  database: {
    connections: number;
    queries: number;
    averageQueryTime: number;
  };
  cache: {
    hits: number;
    misses: number;
    hitRate: number;
  };
  errors: {
    total: number;
    byType: Record<string, number>;
  };
}

export interface BusinessMetrics {
  timestamp: Date;
  users: {
    total: number;
    active: number;
    new: number;
  };
  content: {
    notes: number;
    audioFiles: number;
    transcriptions: number;
  };
  usage: {
    apiCalls: number;
    storageUsed: number;
    transcriptionMinutes: number;
  };
  revenue: {
    subscriptions: {
      free: number;
      premium: number;
      enterprise: number;
    };
    monthlyRecurringRevenue: number;
  };
}

export class MonitoringService {
  private metricsBuffer: {
    system: SystemMetrics[];
    application: ApplicationMetrics[];
    business: BusinessMetrics[];
  } = {
    system: [],
    application: [],
    business: []
  };

  private alertThresholds = {
    cpu: 80, // 80% CPU usage
    memory: 85, // 85% memory usage
    disk: 90, // 90% disk usage
    responseTime: 2000, // 2 seconds
    errorRate: 5, // 5% error rate
    dbConnections: 80 // 80% of max connections
  };

  constructor() {
    this.startMetricsCollection();
  }

  /**
   * Start collecting metrics at regular intervals
   */
  private startMetricsCollection(): void {
    // Collect system metrics every 30 seconds
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);

    // Collect application metrics every minute
    setInterval(() => {
      this.collectApplicationMetrics();
    }, 60000);

    // Collect business metrics every 5 minutes
    setInterval(() => {
      this.collectBusinessMetrics();
    }, 300000);

    // Flush metrics to database every 5 minutes
    setInterval(() => {
      this.flushMetrics();
    }, 300000);

    logger.info('Monitoring service started');
  }

  /**
   * Collect system-level metrics
   */
  private async collectSystemMetrics(): Promise<void> {
    try {
      const cpuUsage = await this.getCpuUsage();
      const memoryInfo = this.getMemoryInfo();
      const diskInfo = await this.getDiskInfo();
      const networkInfo = await this.getNetworkInfo();

      const metrics: SystemMetrics = {
        timestamp: new Date(),
        cpu: {
          usage: cpuUsage,
          loadAverage: os.loadavg()
        },
        memory: memoryInfo,
        disk: diskInfo,
        network: networkInfo
      };

      this.metricsBuffer.system.push(metrics);

      // Check for alerts
      this.checkSystemAlerts(metrics);

      logger.debug('System metrics collected', {
        cpu: metrics.cpu.usage,
        memory: metrics.memory.usage,
        disk: metrics.disk.usage
      });
    } catch (error) {
      logger.error('Failed to collect system metrics', {
        error: error.message
      });
    }
  }

  /**
   * Collect application-level metrics
   */
  private async collectApplicationMetrics(): Promise<void> {
    try {
      // This would typically integrate with your application's metrics
      // For now, we'll simulate some metrics
      const metrics: ApplicationMetrics = {
        timestamp: new Date(),
        requests: {
          total: Math.floor(Math.random() * 1000) + 500,
          successful: Math.floor(Math.random() * 950) + 450,
          failed: Math.floor(Math.random() * 50),
          averageResponseTime: Math.floor(Math.random() * 500) + 100
        },
        database: {
          connections: Math.floor(Math.random() * 20) + 5,
          queries: Math.floor(Math.random() * 2000) + 1000,
          averageQueryTime: Math.floor(Math.random() * 100) + 10
        },
        cache: {
          hits: Math.floor(Math.random() * 800) + 700,
          misses: Math.floor(Math.random() * 200) + 100,
          hitRate: 0
        },
        errors: {
          total: Math.floor(Math.random() * 20),
          byType: {
            'ValidationError': Math.floor(Math.random() * 5),
            'AuthenticationError': Math.floor(Math.random() * 3),
            'DatabaseError': Math.floor(Math.random() * 2),
            'NetworkError': Math.floor(Math.random() * 2)
          }
        }
      };

      metrics.cache.hitRate = metrics.cache.hits / (metrics.cache.hits + metrics.cache.misses);

      this.metricsBuffer.application.push(metrics);

      // Check for alerts
      this.checkApplicationAlerts(metrics);

      logger.debug('Application metrics collected', {
        requests: metrics.requests.total,
        responseTime: metrics.requests.averageResponseTime,
        errors: metrics.errors.total
      });
    } catch (error) {
      logger.error('Failed to collect application metrics', {
        error: error.message
      });
    }
  }

  /**
   * Collect business-level metrics
   */
  private async collectBusinessMetrics(): Promise<void> {
    try {
      const [
        totalUsers,
        activeUsers,
        newUsers,
        totalNotes,
        totalAudioFiles,
        totalTranscriptions,
        subscriptionStats
      ] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({
          where: {
            lastLoginAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
            }
          }
        }),
        prisma.user.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
            }
          }
        }),
        prisma.note.count(),
        prisma.audioFile.count(),
        prisma.transcription.count(),
        prisma.user.groupBy({
          by: ['subscriptionType'],
          _count: { subscriptionType: true }
        })
      ]);

      const subscriptions = {
        free: subscriptionStats.find(s => s.subscriptionType === 'FREE')?._count.subscriptionType || 0,
        premium: subscriptionStats.find(s => s.subscriptionType === 'PREMIUM')?._count.subscriptionType || 0,
        enterprise: subscriptionStats.find(s => s.subscriptionType === 'ENTERPRISE')?._count.subscriptionType || 0
      };

      const metrics: BusinessMetrics = {
        timestamp: new Date(),
        users: {
          total: totalUsers,
          active: activeUsers,
          new: newUsers
        },
        content: {
          notes: totalNotes,
          audioFiles: totalAudioFiles,
          transcriptions: totalTranscriptions
        },
        usage: {
          apiCalls: Math.floor(Math.random() * 10000) + 5000, // Simulated
          storageUsed: Math.floor(Math.random() * 1000000) + 500000, // Simulated in bytes
          transcriptionMinutes: Math.floor(Math.random() * 1000) + 500 // Simulated
        },
        revenue: {
          subscriptions,
          monthlyRecurringRevenue: (subscriptions.premium * 9.99) + (subscriptions.enterprise * 29.99)
        }
      };

      this.metricsBuffer.business.push(metrics);

      logger.debug('Business metrics collected', {
        totalUsers,
        activeUsers,
        newUsers,
        mrr: metrics.revenue.monthlyRecurringRevenue
      });
    } catch (error) {
      logger.error('Failed to collect business metrics', {
        error: error.message
      });
    }
  }

  /**
   * Get CPU usage percentage
   */
  private getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startMeasure = process.cpuUsage();
      const startTime = process.hrtime();

      setTimeout(() => {
        const endMeasure = process.cpuUsage(startMeasure);
        const endTime = process.hrtime(startTime);

        const totalTime = endTime[0] * 1000000 + endTime[1] / 1000; // microseconds
        const totalCpu = endMeasure.user + endMeasure.system;
        const cpuPercent = (totalCpu / totalTime) * 100;

        resolve(Math.min(100, Math.max(0, cpuPercent)));
      }, 100);
    });
  }

  /**
   * Get memory information
   */
  private getMemoryInfo(): SystemMetrics['memory'] {
    const total = os.totalmem();
    const free = os.freemem();
    const used = total - free;
    const usage = (used / total) * 100;

    return {
      total,
      used,
      free,
      usage
    };
  }

  /**
   * Get disk information
   */
  private async getDiskInfo(): Promise<SystemMetrics['disk']> {
    try {
      const stats = fs.statSync(process.cwd());
      // This is a simplified implementation
      // In production, you'd use a library like 'node-disk-info'
      return {
        total: 1000000000, // 1GB simulated
        used: 500000000,   // 500MB simulated
        free: 500000000,   // 500MB simulated
        usage: 50          // 50% simulated
      };
    } catch (error) {
      return {
        total: 0,
        used: 0,
        free: 0,
        usage: 0
      };
    }
  }

  /**
   * Get network information
   */
  private async getNetworkInfo(): Promise<SystemMetrics['network']> {
    try {
      // This would typically integrate with your connection pool
      const activeUsers = await prisma.user.count({
        where: {
          lastLoginAt: {
            gte: new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
          }
        }
      });

      return {
        connections: Math.floor(Math.random() * 100) + 50, // Simulated
        activeUsers
      };
    } catch (error) {
      return {
        connections: 0,
        activeUsers: 0
      };
    }
  }

  /**
   * Check system alerts
   */
  private checkSystemAlerts(metrics: SystemMetrics): void {
    if (metrics.cpu.usage > this.alertThresholds.cpu) {
      this.sendAlert('HIGH_CPU_USAGE', `CPU usage is ${metrics.cpu.usage.toFixed(2)}%`);
    }

    if (metrics.memory.usage > this.alertThresholds.memory) {
      this.sendAlert('HIGH_MEMORY_USAGE', `Memory usage is ${metrics.memory.usage.toFixed(2)}%`);
    }

    if (metrics.disk.usage > this.alertThresholds.disk) {
      this.sendAlert('HIGH_DISK_USAGE', `Disk usage is ${metrics.disk.usage.toFixed(2)}%`);
    }
  }

  /**
   * Check application alerts
   */
  private checkApplicationAlerts(metrics: ApplicationMetrics): void {
    const errorRate = (metrics.errors.total / metrics.requests.total) * 100;

    if (metrics.requests.averageResponseTime > this.alertThresholds.responseTime) {
      this.sendAlert('HIGH_RESPONSE_TIME', `Average response time is ${metrics.requests.averageResponseTime}ms`);
    }

    if (errorRate > this.alertThresholds.errorRate) {
      this.sendAlert('HIGH_ERROR_RATE', `Error rate is ${errorRate.toFixed(2)}%`);
    }

    if (metrics.database.connections > this.alertThresholds.dbConnections) {
      this.sendAlert('HIGH_DB_CONNECTIONS', `Database connections: ${metrics.database.connections}`);
    }
  }

  /**
   * Send alert notification
   */
  private sendAlert(type: string, message: string): void {
    logger.warn('System alert triggered', {
      alertType: type,
      message,
      timestamp: new Date().toISOString()
    });

    // In production, you would integrate with alerting services like:
    // - PagerDuty
    // - Slack
    // - Email notifications
    // - SMS alerts
  }

  /**
   * Flush metrics to database
   */
  private async flushMetrics(): Promise<void> {
    try {
      // In production, you would store these metrics in a time-series database
      // like InfluxDB, Prometheus, or CloudWatch
      
      logger.info('Metrics flushed', {
        systemMetrics: this.metricsBuffer.system.length,
        applicationMetrics: this.metricsBuffer.application.length,
        businessMetrics: this.metricsBuffer.business.length
      });

      // Clear buffers
      this.metricsBuffer.system = [];
      this.metricsBuffer.application = [];
      this.metricsBuffer.business = [];
    } catch (error) {
      logger.error('Failed to flush metrics', {
        error: error.message
      });
    }
  }

  /**
   * Get current metrics summary
   */
  public getCurrentMetrics(): {
    system: SystemMetrics | null;
    application: ApplicationMetrics | null;
    business: BusinessMetrics | null;
  } {
    return {
      system: this.metricsBuffer.system[this.metricsBuffer.system.length - 1] || null,
      application: this.metricsBuffer.application[this.metricsBuffer.application.length - 1] || null,
      business: this.metricsBuffer.business[this.metricsBuffer.business.length - 1] || null
    };
  }

  /**
   * Get health status
   */
  public getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    checks: Record<string, boolean>;
    timestamp: Date;
  } {
    const currentMetrics = this.getCurrentMetrics();
    const checks: Record<string, boolean> = {};

    // System health checks
    if (currentMetrics.system) {
      checks.cpu = currentMetrics.system.cpu.usage < this.alertThresholds.cpu;
      checks.memory = currentMetrics.system.memory.usage < this.alertThresholds.memory;
      checks.disk = currentMetrics.system.disk.usage < this.alertThresholds.disk;
    }

    // Application health checks
    if (currentMetrics.application) {
      checks.responseTime = currentMetrics.application.requests.averageResponseTime < this.alertThresholds.responseTime;
      const errorRate = (currentMetrics.application.errors.total / currentMetrics.application.requests.total) * 100;
      checks.errorRate = errorRate < this.alertThresholds.errorRate;
      checks.database = currentMetrics.application.database.connections < this.alertThresholds.dbConnections;
    }

    const failedChecks = Object.values(checks).filter(check => !check).length;
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    if (failedChecks > 0) {
      status = failedChecks > 2 ? 'critical' : 'warning';
    }

    return {
      status,
      checks,
      timestamp: new Date()
    };
  }
}

export default new MonitoringService();
