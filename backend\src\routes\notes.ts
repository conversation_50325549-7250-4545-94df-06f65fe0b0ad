import { Router } from 'express';
import notesController from '../controllers/notesController';
import { authMiddleware } from '../middleware/auth';
import { rateLimiterMiddleware } from '../middleware/rateLimiter';
import { noteValidations } from '../middleware/validation';

const router = Router();

// Apply authentication to all routes
router.use(authMiddleware.requireAuth);

// Apply rate limiting
router.use(rateLimiterMiddleware.general);

/**
 * @route   POST /api/notes
 * @desc    Create a new note
 * @access  Private
 */
router.post(
  '/',
  rateLimiterMiddleware.subscriptionBased,
  noteValidations.create,
  notesController.createNote
);

/**
 * @route   GET /api/notes
 * @desc    Get all notes for the authenticated user
 * @access  Private
 */
router.get(
  '/',
  noteValidations.list,
  notesController.getNotes
);

/**
 * @route   GET /api/notes/stats
 * @desc    Get note statistics for the user
 * @access  Private
 */
router.get(
  '/stats',
  notesController.getNoteStats
);

/**
 * @route   GET /api/notes/:id
 * @desc    Get a specific note by ID
 * @access  Private
 */
router.get(
  '/:id',
  noteValidations.get,
  notesController.getNote
);

/**
 * @route   PUT /api/notes/:id
 * @desc    Update a note
 * @access  Private
 */
router.put(
  '/:id',
  rateLimiterMiddleware.subscriptionBased,
  noteValidations.update,
  notesController.updateNote
);

/**
 * @route   DELETE /api/notes/:id
 * @desc    Delete a note
 * @access  Private
 */
router.delete(
  '/:id',
  noteValidations.delete,
  notesController.deleteNote
);

/**
 * @route   PATCH /api/notes/:id/archive
 * @desc    Archive or unarchive a note
 * @access  Private
 */
router.patch(
  '/:id/archive',
  noteValidations.get,
  notesController.archiveNote
);

/**
 * @route   PATCH /api/notes/:id/favorite
 * @desc    Toggle favorite status of a note
 * @access  Private
 */
router.patch(
  '/:id/favorite',
  noteValidations.get,
  notesController.toggleFavorite
);

export default router;
