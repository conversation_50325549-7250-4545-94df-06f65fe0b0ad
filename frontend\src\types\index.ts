// User types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
  preferences: UserPreferences;
  subscription: SubscriptionPlan;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  autoSave: boolean;
  notifications: NotificationSettings;
  audioQuality: 'low' | 'medium' | 'high';
  transcriptionLanguage: string;
  speakerIdentification: boolean;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  transcriptionComplete: boolean;
  summaryReady: boolean;
  actionItemReminders: boolean;
}

export interface SubscriptionPlan {
  type: 'free' | 'pro' | 'team' | 'enterprise';
  status: 'active' | 'cancelled' | 'expired';
  expiresAt?: string;
  features: string[];
  limits: {
    monthlyMinutes: number;
    storageGB: number;
    collaborators: number;
  };
}

// Note types
export interface Note {
  id: string;
  title: string;
  content: string;
  summary?: string;
  audioFileId?: string;
  audioUrl?: string;
  audioDuration?: number;
  transcription?: Transcription;
  analysis?: NoteAnalysis;
  tags: string[];
  category: NoteCategory;
  isPublic: boolean;
  collaborators: Collaborator[];
  createdAt: string;
  updatedAt: string;
  userId: string;
  metadata: NoteMetadata;
}

export interface Transcription {
  id: string;
  text: string;
  segments: TranscriptionSegment[];
  speakers: Speaker[];
  language: string;
  confidence: number;
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: string;
}

export interface TranscriptionSegment {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  speakerId?: string;
  confidence: number;
  words: Word[];
}

export interface Word {
  text: string;
  startTime: number;
  endTime: number;
  confidence: number;
}

export interface Speaker {
  id: string;
  name?: string;
  color: string;
  segments: number[];
}

export interface NoteAnalysis {
  summary: string;
  keyPoints: string[];
  actionItems: ActionItem[];
  sentiment: SentimentAnalysis;
  topics: Topic[];
  entities: Entity[];
  decisions: Decision[];
  questions: Question[];
}

export interface ActionItem {
  id: string;
  text: string;
  assignee?: string;
  dueDate?: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in-progress' | 'completed';
  createdAt: string;
}

export interface SentimentAnalysis {
  overall: 'positive' | 'neutral' | 'negative';
  score: number;
  segments: SentimentSegment[];
}

export interface SentimentSegment {
  startTime: number;
  endTime: number;
  sentiment: 'positive' | 'neutral' | 'negative';
  score: number;
}

export interface Topic {
  name: string;
  confidence: number;
  mentions: number;
  segments: number[];
}

export interface Entity {
  text: string;
  type: 'person' | 'organization' | 'location' | 'date' | 'money' | 'other';
  confidence: number;
  mentions: EntityMention[];
}

export interface EntityMention {
  startTime: number;
  endTime: number;
  segmentId: string;
}

export interface Decision {
  text: string;
  participants: string[];
  timestamp: number;
  confidence: number;
}

export interface Question {
  text: string;
  speaker?: string;
  timestamp: number;
  answered: boolean;
  answer?: string;
}

export type NoteCategory = 
  | 'meeting'
  | 'lecture'
  | 'interview'
  | 'brainstorm'
  | 'call'
  | 'presentation'
  | 'other';

export interface Collaborator {
  userId: string;
  email: string;
  name: string;
  role: 'viewer' | 'editor' | 'admin';
  addedAt: string;
}

export interface NoteMetadata {
  source: 'web' | 'mobile' | 'api';
  device?: string;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  meetingInfo?: {
    platform: string;
    participants: string[];
    duration: number;
  };
}

// Audio types
export interface AudioFile {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  duration: number;
  sampleRate: number;
  channels: number;
  url: string;
  waveformData?: number[];
  createdAt: string;
}

export interface RecordingSession {
  id: string;
  status: 'idle' | 'recording' | 'paused' | 'stopped' | 'processing';
  startTime?: number;
  endTime?: number;
  duration: number;
  audioBlob?: Blob;
  liveTranscription: string;
  settings: RecordingSettings;
}

export interface RecordingSettings {
  quality: 'low' | 'medium' | 'high';
  format: 'webm' | 'mp4' | 'wav';
  sampleRate: number;
  channels: number;
  echoCancellation: boolean;
  noiseSuppression: boolean;
  autoGainControl: boolean;
}

// Search types
export interface SearchQuery {
  text: string;
  filters: SearchFilters;
  sort: SearchSort;
  pagination: SearchPagination;
}

export interface SearchFilters {
  dateRange?: {
    start: string;
    end: string;
  };
  categories?: NoteCategory[];
  tags?: string[];
  speakers?: string[];
  hasAudio?: boolean;
  hasTranscription?: boolean;
  sentiment?: ('positive' | 'neutral' | 'negative')[];
}

export interface SearchSort {
  field: 'relevance' | 'date' | 'title' | 'duration';
  direction: 'asc' | 'desc';
}

export interface SearchPagination {
  page: number;
  limit: number;
}

export interface SearchResult {
  note: Note;
  score: number;
  highlights: SearchHighlight[];
}

export interface SearchHighlight {
  field: string;
  text: string;
  startIndex: number;
  endIndex: number;
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  page: number;
  limit: number;
  facets: SearchFacets;
}

export interface SearchFacets {
  categories: FacetCount[];
  tags: FacetCount[];
  speakers: FacetCount[];
  dateRanges: FacetCount[];
}

export interface FacetCount {
  value: string;
  count: number;
}

// API types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  message?: string;
  timestamp: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// WebSocket types
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  id?: string;
}

export interface TranscriptionUpdate {
  sessionId: string;
  text: string;
  isFinal: boolean;
  confidence: number;
  speaker?: string;
  timestamp: number;
}

export interface ProcessingUpdate {
  noteId: string;
  stage: 'transcription' | 'analysis' | 'summary' | 'complete';
  progress: number;
  message?: string;
}

// Component props types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface FormState<T> {
  data: T;
  errors: Record<string, string>;
  isSubmitting: boolean;
  isDirty: boolean;
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
