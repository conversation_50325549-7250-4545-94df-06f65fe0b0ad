import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { NotFoundError, ValidationError, AuthorizationError } from '../middleware/errorHandler';
import { ApiResponse, Note, CreateNoteRequest, UpdateNoteRequest, NotesListResponse } from '../../shared/types/api';

const prisma = new PrismaClient();

export class NotesController {
  
  /**
   * Create a new note
   */
  public createNote = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user!.id;
      const { title, content, category, tags, audioFileId, isPublic } = req.body as CreateNoteRequest;

      // Verify audio file belongs to user if provided
      if (audioFileId) {
        const audioFile = await prisma.audioFile.findFirst({
          where: { id: audioFileId, userId }
        });
        
        if (!audioFile) {
          throw new NotFoundError('Audio file not found');
        }
      }

      const note = await prisma.note.create({
        data: {
          title,
          content: content || '',
          category: category || 'OTHER',
          tags: tags || [],
          audioFileId,
          isPublic: isPublic || false,
          userId,
          metadata: {}
        },
        include: {
          audioFile: true,
          transcription: true,
          analysis: true,
          user: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      logger.info('Note created', {
        noteId: note.id,
        userId,
        title: note.title,
        category: note.category
      });

      const response: ApiResponse<Note> = {
        success: true,
        data: note as Note,
        message: 'Note created successfully',
        timestamp: new Date().toISOString()
      };

      res.status(201).json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get all notes for the authenticated user
   */
  public getNotes = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user!.id;
      const {
        page = 1,
        limit = 20,
        q: searchQuery,
        category,
        dateFrom,
        dateTo,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        isArchived = false,
        isFavorite
      } = req.query;

      const skip = (Number(page) - 1) * Number(limit);
      const take = Number(limit);

      // Build where clause
      const where: any = {
        userId,
        isArchived: Boolean(isArchived)
      };

      if (searchQuery) {
        where.OR = [
          { title: { contains: searchQuery as string, mode: 'insensitive' } },
          { content: { contains: searchQuery as string, mode: 'insensitive' } },
          { tags: { has: searchQuery as string } }
        ];
      }

      if (category) {
        where.category = category;
      }

      if (isFavorite !== undefined) {
        where.isFavorite = Boolean(isFavorite);
      }

      if (dateFrom || dateTo) {
        where.createdAt = {};
        if (dateFrom) where.createdAt.gte = new Date(dateFrom as string);
        if (dateTo) where.createdAt.lte = new Date(dateTo as string);
      }

      // Get notes with total count
      const [notes, total] = await Promise.all([
        prisma.note.findMany({
          where,
          skip,
          take,
          orderBy: { [sortBy as string]: sortOrder },
          include: {
            audioFile: {
              select: {
                id: true,
                filename: true,
                duration: true,
                size: true,
                url: true
              }
            },
            transcription: {
              select: {
                id: true,
                language: true,
                confidence: true,
                processingStatus: true
              }
            },
            analysis: {
              select: {
                id: true,
                summary: true,
                processingStatus: true
              }
            },
            user: {
              select: { id: true, name: true, email: true }
            }
          }
        }),
        prisma.note.count({ where })
      ]);

      const response: ApiResponse<NotesListResponse> = {
        success: true,
        data: {
          notes: notes as Note[],
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            totalPages: Math.ceil(total / Number(limit))
          }
        },
        message: 'Notes retrieved successfully',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get a specific note by ID
   */
  public getNote = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      const note = await prisma.note.findFirst({
        where: {
          id,
          OR: [
            { userId }, // User's own note
            { isPublic: true } // Public note
          ]
        },
        include: {
          audioFile: true,
          transcription: {
            include: {
              audioFile: {
                select: { id: true, filename: true, url: true }
              }
            }
          },
          analysis: true,
          user: {
            select: { id: true, name: true, email: true }
          },
          collaborations: {
            include: {
              user: {
                select: { id: true, name: true, email: true }
              }
            }
          }
        }
      });

      if (!note) {
        throw new NotFoundError('Note not found');
      }

      const response: ApiResponse<Note> = {
        success: true,
        data: note as Note,
        message: 'Note retrieved successfully',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Update a note
   */
  public updateNote = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      const updateData = req.body as UpdateNoteRequest;

      // Check if note exists and belongs to user
      const existingNote = await prisma.note.findFirst({
        where: { id, userId }
      });

      if (!existingNote) {
        throw new NotFoundError('Note not found');
      }

      // Update note
      const note = await prisma.note.update({
        where: { id },
        data: {
          ...updateData,
          updatedAt: new Date()
        },
        include: {
          audioFile: true,
          transcription: true,
          analysis: true,
          user: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      logger.info('Note updated', {
        noteId: note.id,
        userId,
        changes: Object.keys(updateData)
      });

      const response: ApiResponse<Note> = {
        success: true,
        data: note as Note,
        message: 'Note updated successfully',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Delete a note
   */
  public deleteNote = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      // Check if note exists and belongs to user
      const existingNote = await prisma.note.findFirst({
        where: { id, userId }
      });

      if (!existingNote) {
        throw new NotFoundError('Note not found');
      }

      // Delete note (cascade will handle related records)
      await prisma.note.delete({
        where: { id }
      });

      logger.info('Note deleted', {
        noteId: id,
        userId,
        title: existingNote.title
      });

      const response: ApiResponse<null> = {
        success: true,
        data: null,
        message: 'Note deleted successfully',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Archive/unarchive a note
   */
  public archiveNote = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const { isArchived } = req.body;
      const userId = req.user!.id;

      const note = await prisma.note.updateMany({
        where: { id, userId },
        data: { isArchived: Boolean(isArchived) }
      });

      if (note.count === 0) {
        throw new NotFoundError('Note not found');
      }

      const response: ApiResponse<null> = {
        success: true,
        data: null,
        message: `Note ${isArchived ? 'archived' : 'unarchived'} successfully`,
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Toggle favorite status of a note
   */
  public toggleFavorite = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      // Get current favorite status
      const currentNote = await prisma.note.findFirst({
        where: { id, userId },
        select: { isFavorite: true }
      });

      if (!currentNote) {
        throw new NotFoundError('Note not found');
      }

      // Toggle favorite status
      const note = await prisma.note.update({
        where: { id },
        data: { isFavorite: !currentNote.isFavorite },
        include: {
          audioFile: true,
          transcription: true,
          analysis: true,
          user: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      const response: ApiResponse<Note> = {
        success: true,
        data: note as Note,
        message: `Note ${note.isFavorite ? 'added to' : 'removed from'} favorites`,
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get note statistics for the user
   */
  public getNoteStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user!.id;

      const [
        totalNotes,
        archivedNotes,
        favoriteNotes,
        categoryStats,
        recentNotes
      ] = await Promise.all([
        prisma.note.count({ where: { userId, isArchived: false } }),
        prisma.note.count({ where: { userId, isArchived: true } }),
        prisma.note.count({ where: { userId, isFavorite: true } }),
        prisma.note.groupBy({
          by: ['category'],
          where: { userId, isArchived: false },
          _count: { category: true }
        }),
        prisma.note.count({
          where: {
            userId,
            createdAt: { gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
          }
        })
      ]);

      const stats = {
        totalNotes,
        archivedNotes,
        favoriteNotes,
        recentNotes,
        categoryBreakdown: categoryStats.map(stat => ({
          category: stat.category,
          count: stat._count.category
        }))
      };

      const response: ApiResponse<typeof stats> = {
        success: true,
        data: stats,
        message: 'Note statistics retrieved successfully',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  };
}

export default new NotesController();
