import { PrismaClient, Note, NoteCategory } from '@prisma/client';
import { logger } from '../utils/logger';
import { NotFoundError, ValidationError, AuthorizationError } from '../middleware/errorHandler';

const prisma = new PrismaClient();

export interface CreateNoteData {
  title: string;
  content?: string;
  category?: NoteCategory;
  tags?: string[];
  audioFileId?: string;
  isPublic?: boolean;
  userId: string;
}

export interface UpdateNoteData {
  title?: string;
  content?: string;
  category?: NoteCategory;
  tags?: string[];
  isPublic?: boolean;
  isFavorite?: boolean;
  isArchived?: boolean;
}

export interface NoteFilters {
  category?: NoteCategory;
  tags?: string[];
  isArchived?: boolean;
  isFavorite?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
  searchQuery?: string;
}

export interface NotePagination {
  page: number;
  limit: number;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export class NoteService {
  
  /**
   * Create a new note
   */
  async createNote(data: CreateNoteData): Promise<Note> {
    try {
      // Validate audio file ownership if provided
      if (data.audioFileId) {
        const audioFile = await prisma.audioFile.findFirst({
          where: { id: data.audioFileId, userId: data.userId }
        });
        
        if (!audioFile) {
          throw new NotFoundError('Audio file not found or access denied');
        }
      }

      const note = await prisma.note.create({
        data: {
          title: data.title,
          content: data.content || '',
          category: data.category || 'OTHER',
          tags: data.tags || [],
          audioFileId: data.audioFileId,
          isPublic: data.isPublic || false,
          userId: data.userId,
          metadata: {}
        },
        include: {
          audioFile: true,
          transcription: true,
          analysis: true,
          user: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      logger.info('Note created', {
        noteId: note.id,
        userId: data.userId,
        title: note.title
      });

      return note;
    } catch (error) {
      logger.error('Failed to create note', {
        userId: data.userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get notes with filtering and pagination
   */
  async getNotes(
    userId: string,
    filters: NoteFilters,
    pagination: NotePagination
  ): Promise<{
    notes: Note[];
    total: number;
    totalPages: number;
  }> {
    try {
      const { page, limit, sortBy, sortOrder } = pagination;
      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {
        userId,
        isArchived: filters.isArchived || false
      };

      if (filters.category) {
        where.category = filters.category;
      }

      if (filters.isFavorite !== undefined) {
        where.isFavorite = filters.isFavorite;
      }

      if (filters.tags && filters.tags.length > 0) {
        where.tags = { hasSome: filters.tags };
      }

      if (filters.dateFrom || filters.dateTo) {
        where.createdAt = {};
        if (filters.dateFrom) where.createdAt.gte = filters.dateFrom;
        if (filters.dateTo) where.createdAt.lte = filters.dateTo;
      }

      if (filters.searchQuery) {
        where.OR = [
          { title: { contains: filters.searchQuery, mode: 'insensitive' } },
          { content: { contains: filters.searchQuery, mode: 'insensitive' } },
          { tags: { has: filters.searchQuery } }
        ];
      }

      const [notes, total] = await Promise.all([
        prisma.note.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            audioFile: {
              select: {
                id: true,
                filename: true,
                duration: true,
                size: true,
                url: true
              }
            },
            transcription: {
              select: {
                id: true,
                language: true,
                confidence: true,
                processingStatus: true
              }
            },
            analysis: {
              select: {
                id: true,
                summary: true,
                processingStatus: true
              }
            },
            user: {
              select: { id: true, name: true, email: true }
            }
          }
        }),
        prisma.note.count({ where })
      ]);

      return {
        notes,
        total,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('Failed to get notes', {
        userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get a single note by ID
   */
  async getNoteById(noteId: string, userId: string): Promise<Note> {
    try {
      const note = await prisma.note.findFirst({
        where: {
          id: noteId,
          OR: [
            { userId }, // User's own note
            { isPublic: true } // Public note
          ]
        },
        include: {
          audioFile: true,
          transcription: {
            include: {
              audioFile: {
                select: { id: true, filename: true, url: true }
              }
            }
          },
          analysis: true,
          user: {
            select: { id: true, name: true, email: true }
          },
          collaborations: {
            include: {
              user: {
                select: { id: true, name: true, email: true }
              }
            }
          }
        }
      });

      if (!note) {
        throw new NotFoundError('Note not found');
      }

      return note;
    } catch (error) {
      logger.error('Failed to get note', {
        noteId,
        userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Update a note
   */
  async updateNote(noteId: string, userId: string, data: UpdateNoteData): Promise<Note> {
    try {
      // Check if note exists and belongs to user
      const existingNote = await prisma.note.findFirst({
        where: { id: noteId, userId }
      });

      if (!existingNote) {
        throw new NotFoundError('Note not found');
      }

      const note = await prisma.note.update({
        where: { id: noteId },
        data: {
          ...data,
          updatedAt: new Date()
        },
        include: {
          audioFile: true,
          transcription: true,
          analysis: true,
          user: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      logger.info('Note updated', {
        noteId,
        userId,
        changes: Object.keys(data)
      });

      return note;
    } catch (error) {
      logger.error('Failed to update note', {
        noteId,
        userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Delete a note
   */
  async deleteNote(noteId: string, userId: string): Promise<void> {
    try {
      const existingNote = await prisma.note.findFirst({
        where: { id: noteId, userId }
      });

      if (!existingNote) {
        throw new NotFoundError('Note not found');
      }

      await prisma.note.delete({
        where: { id: noteId }
      });

      logger.info('Note deleted', {
        noteId,
        userId,
        title: existingNote.title
      });
    } catch (error) {
      logger.error('Failed to delete note', {
        noteId,
        userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Toggle favorite status
   */
  async toggleFavorite(noteId: string, userId: string): Promise<Note> {
    try {
      const currentNote = await prisma.note.findFirst({
        where: { id: noteId, userId },
        select: { isFavorite: true }
      });

      if (!currentNote) {
        throw new NotFoundError('Note not found');
      }

      const note = await prisma.note.update({
        where: { id: noteId },
        data: { isFavorite: !currentNote.isFavorite },
        include: {
          audioFile: true,
          transcription: true,
          analysis: true,
          user: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      logger.info('Note favorite toggled', {
        noteId,
        userId,
        isFavorite: note.isFavorite
      });

      return note;
    } catch (error) {
      logger.error('Failed to toggle favorite', {
        noteId,
        userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Archive/unarchive a note
   */
  async archiveNote(noteId: string, userId: string, isArchived: boolean): Promise<void> {
    try {
      const result = await prisma.note.updateMany({
        where: { id: noteId, userId },
        data: { isArchived }
      });

      if (result.count === 0) {
        throw new NotFoundError('Note not found');
      }

      logger.info('Note archive status changed', {
        noteId,
        userId,
        isArchived
      });
    } catch (error) {
      logger.error('Failed to archive note', {
        noteId,
        userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get note statistics
   */
  async getNoteStats(userId: string): Promise<{
    totalNotes: number;
    archivedNotes: number;
    favoriteNotes: number;
    recentNotes: number;
    categoryBreakdown: Array<{ category: string; count: number }>;
  }> {
    try {
      const [
        totalNotes,
        archivedNotes,
        favoriteNotes,
        categoryStats,
        recentNotes
      ] = await Promise.all([
        prisma.note.count({ where: { userId, isArchived: false } }),
        prisma.note.count({ where: { userId, isArchived: true } }),
        prisma.note.count({ where: { userId, isFavorite: true } }),
        prisma.note.groupBy({
          by: ['category'],
          where: { userId, isArchived: false },
          _count: { category: true }
        }),
        prisma.note.count({
          where: {
            userId,
            createdAt: { gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
          }
        })
      ]);

      return {
        totalNotes,
        archivedNotes,
        favoriteNotes,
        recentNotes,
        categoryBreakdown: categoryStats.map(stat => ({
          category: stat.category,
          count: stat._count.category
        }))
      };
    } catch (error) {
      logger.error('Failed to get note stats', {
        userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get popular tags for a user
   */
  async getPopularTags(userId: string, limit: number = 20): Promise<Array<{ tag: string; count: number }>> {
    try {
      const notes = await prisma.note.findMany({
        where: { userId, isArchived: false },
        select: { tags: true }
      });

      const tagCounts = new Map<string, number>();
      
      notes.forEach(note => {
        note.tags.forEach(tag => {
          tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
        });
      });

      return Array.from(tagCounts.entries())
        .map(([tag, count]) => ({ tag, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, limit);
    } catch (error) {
      logger.error('Failed to get popular tags', {
        userId,
        error: error.message
      });
      throw error;
    }
  }
}

export default new NoteService();
