import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import ffmpeg from 'fluent-ffmpeg';
import { logger } from '../utils/logger';
import { NotFoundError, ValidationError, ServiceUnavailableError } from '../middleware/errorHandler';
import { ApiResponse, AudioFile, AudioUploadResponse } from '../../shared/types/api';

const prisma = new PrismaClient();

// Configure multer for audio file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'audio');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedMimeTypes = [
    'audio/mpeg',
    'audio/wav',
    'audio/mp4',
    'audio/webm',
    'audio/ogg',
    'audio/x-m4a'
  ];
  
  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new ValidationError('Invalid audio file format'));
  }
};

export const audioUpload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
    files: 1
  }
});

export class AudioController {
  
  /**
   * Upload audio file
   */
  public uploadAudio = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user!.id;
      const file = req.file;
      
      if (!file) {
        throw new ValidationError('Audio file is required');
      }

      const { title, language = 'en', speakerIdentification = false } = req.body;

      // Get audio metadata using ffmpeg
      const audioInfo = await this.getAudioInfo(file.path);
      
      // Create audio file record
      const audioFile = await prisma.audioFile.create({
        data: {
          filename: file.filename,
          originalName: file.originalname,
          mimeType: file.mimetype,
          size: file.size,
          duration: audioInfo.duration,
          sampleRate: audioInfo.sampleRate,
          channels: audioInfo.channels,
          url: `/uploads/audio/${file.filename}`,
          userId,
          processingStatus: 'PENDING'
        }
      });

      // Create initial note if title provided
      let note = null;
      if (title) {
        note = await prisma.note.create({
          data: {
            title,
            content: '',
            audioFileId: audioFile.id,
            userId,
            category: 'OTHER'
          }
        });
      }

      // Queue for transcription processing
      await this.queueTranscription(audioFile.id, { language, speakerIdentification });

      logger.info('Audio file uploaded', {
        audioFileId: audioFile.id,
        userId,
        filename: file.originalname,
        size: file.size,
        duration: audioInfo.duration
      });

      const response: ApiResponse<AudioUploadResponse> = {
        success: true,
        data: {
          audioFile: audioFile as AudioFile,
          note: note as any
        },
        message: 'Audio file uploaded successfully',
        timestamp: new Date().toISOString()
      };

      res.status(201).json(response);
    } catch (error) {
      // Clean up uploaded file on error
      if (req.file) {
        fs.unlink(req.file.path, () => {});
      }
      next(error);
    }
  };

  /**
   * Get audio file by ID
   */
  public getAudio = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      const audioFile = await prisma.audioFile.findFirst({
        where: { id, userId },
        include: {
          transcriptions: {
            select: {
              id: true,
              text: true,
              language: true,
              confidence: true,
              processingStatus: true,
              segments: true,
              speakers: true
            }
          },
          notes: {
            select: {
              id: true,
              title: true,
              category: true,
              createdAt: true
            }
          }
        }
      });

      if (!audioFile) {
        throw new NotFoundError('Audio file not found');
      }

      const response: ApiResponse<AudioFile> = {
        success: true,
        data: audioFile as AudioFile,
        message: 'Audio file retrieved successfully',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get all audio files for user
   */
  public getAudioFiles = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user!.id;
      const { page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

      const skip = (Number(page) - 1) * Number(limit);
      const take = Number(limit);

      const [audioFiles, total] = await Promise.all([
        prisma.audioFile.findMany({
          where: { userId },
          skip,
          take,
          orderBy: { [sortBy as string]: sortOrder },
          include: {
            transcriptions: {
              select: {
                id: true,
                processingStatus: true,
                confidence: true
              }
            },
            notes: {
              select: {
                id: true,
                title: true
              }
            }
          }
        }),
        prisma.audioFile.count({ where: { userId } })
      ]);

      const response: ApiResponse<{
        audioFiles: AudioFile[];
        pagination: {
          page: number;
          limit: number;
          total: number;
          totalPages: number;
        };
      }> = {
        success: true,
        data: {
          audioFiles: audioFiles as AudioFile[],
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            totalPages: Math.ceil(total / Number(limit))
          }
        },
        message: 'Audio files retrieved successfully',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Delete audio file
   */
  public deleteAudio = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      const audioFile = await prisma.audioFile.findFirst({
        where: { id, userId }
      });

      if (!audioFile) {
        throw new NotFoundError('Audio file not found');
      }

      // Delete physical file
      const filePath = path.join(process.cwd(), 'uploads', 'audio', audioFile.filename);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      // Delete database record (cascade will handle related records)
      await prisma.audioFile.delete({
        where: { id }
      });

      logger.info('Audio file deleted', {
        audioFileId: id,
        userId,
        filename: audioFile.originalName
      });

      const response: ApiResponse<null> = {
        success: true,
        data: null,
        message: 'Audio file deleted successfully',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Stream audio file
   */
  public streamAudio = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      const audioFile = await prisma.audioFile.findFirst({
        where: { id, userId }
      });

      if (!audioFile) {
        throw new NotFoundError('Audio file not found');
      }

      const filePath = path.join(process.cwd(), 'uploads', 'audio', audioFile.filename);
      
      if (!fs.existsSync(filePath)) {
        throw new NotFoundError('Audio file not found on disk');
      }

      const stat = fs.statSync(filePath);
      const fileSize = stat.size;
      const range = req.headers.range;

      if (range) {
        // Handle range requests for audio streaming
        const parts = range.replace(/bytes=/, "").split("-");
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
        const chunksize = (end - start) + 1;
        const file = fs.createReadStream(filePath, { start, end });
        
        res.writeHead(206, {
          'Content-Range': `bytes ${start}-${end}/${fileSize}`,
          'Accept-Ranges': 'bytes',
          'Content-Length': chunksize,
          'Content-Type': audioFile.mimeType,
        });
        
        file.pipe(res);
      } else {
        // Send entire file
        res.writeHead(200, {
          'Content-Length': fileSize,
          'Content-Type': audioFile.mimeType,
        });
        
        fs.createReadStream(filePath).pipe(res);
      }
    } catch (error) {
      next(error);
    }
  };

  /**
   * Trigger transcription for audio file
   */
  public transcribeAudio = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      const { language = 'en', speakerIdentification = false, timestamps = true } = req.body;

      const audioFile = await prisma.audioFile.findFirst({
        where: { id, userId }
      });

      if (!audioFile) {
        throw new NotFoundError('Audio file not found');
      }

      // Check if transcription already exists
      const existingTranscription = await prisma.transcription.findFirst({
        where: { audioFileId: id }
      });

      if (existingTranscription && existingTranscription.processingStatus === 'COMPLETED') {
        const response: ApiResponse<any> = {
          success: true,
          data: existingTranscription,
          message: 'Transcription already exists',
          timestamp: new Date().toISOString()
        };
        return res.json(response);
      }

      // Queue for transcription
      await this.queueTranscription(id, { language, speakerIdentification, timestamps });

      const response: ApiResponse<null> = {
        success: true,
        data: null,
        message: 'Transcription queued successfully',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get audio file metadata using ffmpeg
   */
  private getAudioInfo = (filePath: string): Promise<{
    duration: number;
    sampleRate: number;
    channels: number;
  }> => {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(new ValidationError('Invalid audio file'));
          return;
        }

        const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');
        
        if (!audioStream) {
          reject(new ValidationError('No audio stream found'));
          return;
        }

        resolve({
          duration: metadata.format.duration || 0,
          sampleRate: audioStream.sample_rate || 44100,
          channels: audioStream.channels || 1
        });
      });
    });
  };

  /**
   * Queue audio file for transcription processing
   */
  private async queueTranscription(audioFileId: string, options: {
    language: string;
    speakerIdentification: boolean;
    timestamps?: boolean;
  }): Promise<void> {
    try {
      // Create or update transcription record
      await prisma.transcription.upsert({
        where: { audioFileId },
        update: {
          processingStatus: 'PENDING'
        },
        create: {
          audioFileId,
          text: '',
          language: options.language,
          processingStatus: 'PENDING',
          segments: [],
          speakers: []
        }
      });

      // Update audio file status
      await prisma.audioFile.update({
        where: { id: audioFileId },
        data: { processingStatus: 'PROCESSING' }
      });

      // TODO: Queue job for background processing
      // This would typically use a job queue like Bull or Agenda
      logger.info('Transcription queued', {
        audioFileId,
        options
      });

    } catch (error) {
      logger.error('Failed to queue transcription', {
        audioFileId,
        error: error.message
      });
      throw new ServiceUnavailableError('Failed to queue transcription');
    }
  }
}

export default new AudioController();
