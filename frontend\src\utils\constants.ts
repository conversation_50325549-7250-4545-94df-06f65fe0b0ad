// Storage keys for localStorage
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'quickcapt_auth_token',
  REFRESH_TOKEN: 'quickcapt_refresh_token',
  USER_DATA: 'quickcapt_user_data',
  THEME: 'quickcapt_theme',
  LANGUAGE: 'quickcapt_language',
  RECORDING_SETTINGS: 'quickcapt_recording_settings',
  SEARCH_HISTORY: 'quickcapt_search_history',
} as const;

// API endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
    CHANGE_PASSWORD: '/auth/change-password',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
  },
  NOTES: {
    BASE: '/notes',
    STATS: '/notes/stats',
    ARCHIVE: (id: string) => `/notes/${id}/archive`,
    FAVORITE: (id: string) => `/notes/${id}/favorite`,
  },
  AUDIO: {
    BASE: '/audio',
    UPLOAD: '/audio/upload',
    TRANSCRIBE: (id: string) => `/audio/${id}/transcribe`,
    STREAM: (id: string) => `/audio/${id}/stream`,
  },
  SEARCH: {
    BASE: '/search',
    SUGGESTIONS: '/search/suggestions',
    FACETS: '/search/facets',
  },
} as const;

// WebSocket events
export const WS_EVENTS = {
  // Connection
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  ERROR: 'error',
  
  // Authentication
  AUTHENTICATE: 'authenticate',
  AUTHENTICATED: 'authenticated',
  
  // Transcription
  START_TRANSCRIPTION: 'start_transcription',
  STOP_TRANSCRIPTION: 'stop_transcription',
  TRANSCRIPTION_UPDATE: 'transcription_update',
  TRANSCRIPTION_COMPLETE: 'transcription_complete',
  TRANSCRIPTION_ERROR: 'transcription_error',
  
  // Processing
  PROCESSING_UPDATE: 'processing_update',
  PROCESSING_COMPLETE: 'processing_complete',
  PROCESSING_ERROR: 'processing_error',
  
  // Collaboration
  JOIN_NOTE: 'join_note',
  LEAVE_NOTE: 'leave_note',
  NOTE_UPDATE: 'note_update',
  USER_TYPING: 'user_typing',
  USER_STOPPED_TYPING: 'user_stopped_typing',
  
  // System
  HEARTBEAT: 'heartbeat',
  HEARTBEAT_RESPONSE: 'heartbeat_response',
} as const;

// Application constants
export const APP_CONFIG = {
  NAME: 'QuickCapt',
  VERSION: '1.0.0',
  DESCRIPTION: 'AI-Powered Note Taking Revolution',
  
  // Limits
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  MAX_TITLE_LENGTH: 200,
  MAX_CONTENT_LENGTH: 50000,
  MAX_TAGS: 20,
  MAX_TAG_LENGTH: 50,
  
  // Audio settings
  SUPPORTED_AUDIO_FORMATS: ['mp3', 'wav', 'm4a', 'webm', 'ogg'],
  DEFAULT_SAMPLE_RATE: 44100,
  DEFAULT_CHANNELS: 1,
  
  // Pagination
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // Search
  MIN_SEARCH_LENGTH: 2,
  MAX_SEARCH_SUGGESTIONS: 10,
  SEARCH_DEBOUNCE_MS: 300,
  
  // WebSocket
  WS_RECONNECT_ATTEMPTS: 5,
  WS_RECONNECT_INTERVAL: 5000,
  WS_HEARTBEAT_INTERVAL: 30000,
} as const;

// Note categories
export const NOTE_CATEGORIES = {
  MEETING: 'MEETING',
  LECTURE: 'LECTURE',
  INTERVIEW: 'INTERVIEW',
  PERSONAL: 'PERSONAL',
  OTHER: 'OTHER',
} as const;

export const NOTE_CATEGORY_LABELS = {
  [NOTE_CATEGORIES.MEETING]: 'Meeting',
  [NOTE_CATEGORIES.LECTURE]: 'Lecture',
  [NOTE_CATEGORIES.INTERVIEW]: 'Interview',
  [NOTE_CATEGORIES.PERSONAL]: 'Personal',
  [NOTE_CATEGORIES.OTHER]: 'Other',
} as const;

export const NOTE_CATEGORY_COLORS = {
  [NOTE_CATEGORIES.MEETING]: 'bg-blue-100 text-blue-800',
  [NOTE_CATEGORIES.LECTURE]: 'bg-green-100 text-green-800',
  [NOTE_CATEGORIES.INTERVIEW]: 'bg-purple-100 text-purple-800',
  [NOTE_CATEGORIES.PERSONAL]: 'bg-yellow-100 text-yellow-800',
  [NOTE_CATEGORIES.OTHER]: 'bg-gray-100 text-gray-800',
} as const;

// Processing statuses
export const PROCESSING_STATUS = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
} as const;

export const PROCESSING_STATUS_LABELS = {
  [PROCESSING_STATUS.PENDING]: 'Pending',
  [PROCESSING_STATUS.PROCESSING]: 'Processing',
  [PROCESSING_STATUS.COMPLETED]: 'Completed',
  [PROCESSING_STATUS.FAILED]: 'Failed',
} as const;

export const PROCESSING_STATUS_COLORS = {
  [PROCESSING_STATUS.PENDING]: 'bg-gray-100 text-gray-800',
  [PROCESSING_STATUS.PROCESSING]: 'bg-yellow-100 text-yellow-800',
  [PROCESSING_STATUS.COMPLETED]: 'bg-green-100 text-green-800',
  [PROCESSING_STATUS.FAILED]: 'bg-red-100 text-red-800',
} as const;

// User subscription types
export const SUBSCRIPTION_TYPES = {
  FREE: 'FREE',
  PREMIUM: 'PREMIUM',
  ENTERPRISE: 'ENTERPRISE',
} as const;

export const SUBSCRIPTION_LIMITS = {
  [SUBSCRIPTION_TYPES.FREE]: {
    monthlyUploads: 10,
    storageGB: 0.1, // 100MB
    transcriptionMinutes: 60,
    collaborators: 0,
  },
  [SUBSCRIPTION_TYPES.PREMIUM]: {
    monthlyUploads: 100,
    storageGB: 1, // 1GB
    transcriptionMinutes: 600, // 10 hours
    collaborators: 5,
  },
  [SUBSCRIPTION_TYPES.ENTERPRISE]: {
    monthlyUploads: -1, // Unlimited
    storageGB: -1, // Unlimited
    transcriptionMinutes: -1, // Unlimited
    collaborators: -1, // Unlimited
  },
} as const;

// Theme constants
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
} as const;

// Language constants
export const SUPPORTED_LANGUAGES = {
  EN: 'en',
  ES: 'es',
  FR: 'fr',
  DE: 'de',
  IT: 'it',
  PT: 'pt',
  RU: 'ru',
  JA: 'ja',
  KO: 'ko',
  ZH: 'zh',
} as const;

export const LANGUAGE_LABELS = {
  [SUPPORTED_LANGUAGES.EN]: 'English',
  [SUPPORTED_LANGUAGES.ES]: 'Español',
  [SUPPORTED_LANGUAGES.FR]: 'Français',
  [SUPPORTED_LANGUAGES.DE]: 'Deutsch',
  [SUPPORTED_LANGUAGES.IT]: 'Italiano',
  [SUPPORTED_LANGUAGES.PT]: 'Português',
  [SUPPORTED_LANGUAGES.RU]: 'Русский',
  [SUPPORTED_LANGUAGES.JA]: '日本語',
  [SUPPORTED_LANGUAGES.KO]: '한국어',
  [SUPPORTED_LANGUAGES.ZH]: '中文',
} as const;

// Error codes
export const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  UNSUPPORTED_FORMAT: 'UNSUPPORTED_FORMAT',
  TRANSCRIPTION_FAILED: 'TRANSCRIPTION_FAILED',
  STORAGE_FULL: 'STORAGE_FULL',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  NOTE_CREATED: 'Note created successfully',
  NOTE_UPDATED: 'Note updated successfully',
  NOTE_DELETED: 'Note deleted successfully',
  AUDIO_UPLOADED: 'Audio file uploaded successfully',
  TRANSCRIPTION_STARTED: 'Transcription started',
  PROFILE_UPDATED: 'Profile updated successfully',
  PASSWORD_CHANGED: 'Password changed successfully',
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  REGISTRATION_SUCCESS: 'Registration successful',
} as const;

// Animation durations (in milliseconds)
export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const;

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const;

export default {
  STORAGE_KEYS,
  API_ENDPOINTS,
  WS_EVENTS,
  APP_CONFIG,
  NOTE_CATEGORIES,
  NOTE_CATEGORY_LABELS,
  NOTE_CATEGORY_COLORS,
  PROCESSING_STATUS,
  PROCESSING_STATUS_LABELS,
  PROCESSING_STATUS_COLORS,
  SUBSCRIPTION_TYPES,
  SUBSCRIPTION_LIMITS,
  THEMES,
  SUPPORTED_LANGUAGES,
  LANGUAGE_LABELS,
  ERROR_CODES,
  SUCCESS_MESSAGES,
  ANIMATION_DURATIONS,
  BREAKPOINTS,
};
