import { useState, useEffect, useContext, createContext, ReactNode, useCallback } from 'react';
import { useWebSocket } from './useWebSocket';
import { useAuth } from './useAuth';
import toast from 'react-hot-toast';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  createdAt: string;
  data?: Record<string, any>;
  actions?: NotificationAction[];
}

interface NotificationAction {
  id: string;
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt' | 'read'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  clearAll: () => void;
  requestPermission: () => Promise<boolean>;
  isPermissionGranted: boolean;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isPermissionGranted, setIsPermissionGranted] = useState(false);
  const { isAuthenticated } = useAuth();
  const { lastMessage } = useWebSocket('/api/ws/notifications');

  // Check notification permission on mount
  useEffect(() => {
    if ('Notification' in window) {
      setIsPermissionGranted(Notification.permission === 'granted');
    }
  }, []);

  // Handle WebSocket notifications
  useEffect(() => {
    if (lastMessage?.type === 'notification') {
      const notificationData = lastMessage.payload;
      addNotification({
        title: notificationData.title,
        message: notificationData.message,
        type: notificationData.type || 'info',
        data: notificationData.data,
        actions: notificationData.actions
      });
    }
  }, [lastMessage]);

  // Load notifications from localStorage on mount
  useEffect(() => {
    if (isAuthenticated) {
      try {
        const saved = localStorage.getItem('quickcapt_notifications');
        if (saved) {
          const parsedNotifications = JSON.parse(saved);
          setNotifications(parsedNotifications);
        }
      } catch (error) {
        console.error('Failed to load notifications from localStorage:', error);
      }
    }
  }, [isAuthenticated]);

  // Save notifications to localStorage when they change
  useEffect(() => {
    if (isAuthenticated) {
      try {
        localStorage.setItem('quickcapt_notifications', JSON.stringify(notifications));
      } catch (error) {
        console.error('Failed to save notifications to localStorage:', error);
      }
    }
  }, [notifications, isAuthenticated]);

  const generateId = () => `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const addNotification = useCallback((notificationData: Omit<Notification, 'id' | 'createdAt' | 'read'>) => {
    const notification: Notification = {
      ...notificationData,
      id: generateId(),
      createdAt: new Date().toISOString(),
      read: false
    };

    setNotifications(prev => [notification, ...prev.slice(0, 99)]); // Keep max 100 notifications

    // Show toast notification
    const toastOptions = {
      duration: 4000,
      position: 'top-right' as const,
    };

    switch (notification.type) {
      case 'success':
        toast.success(notification.message, toastOptions);
        break;
      case 'error':
        toast.error(notification.message, toastOptions);
        break;
      case 'warning':
        toast(notification.message, { ...toastOptions, icon: '⚠️' });
        break;
      default:
        toast(notification.message, toastOptions);
    }

    // Show browser notification if permission granted
    if (isPermissionGranted && document.hidden) {
      showBrowserNotification(notification);
    }
  }, [isPermissionGranted]);

  const showBrowserNotification = (notification: Notification) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/logo192.png',
        badge: '/badge-72x72.png',
        tag: notification.id,
        requireInteraction: notification.type === 'error',
        data: notification.data
      });

      browserNotification.onclick = () => {
        window.focus();
        markAsRead(notification.id);
        browserNotification.close();
      };

      // Auto-close after 5 seconds
      setTimeout(() => {
        browserNotification.close();
      }, 5000);
    }
  };

  const markAsRead = useCallback((id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  }, []);

  const markAllAsRead = useCallback(() => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      setIsPermissionGranted(true);
      return true;
    }

    if (Notification.permission === 'denied') {
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      const granted = permission === 'granted';
      setIsPermissionGranted(granted);
      
      if (granted) {
        toast.success('Notifications enabled successfully');
      } else {
        toast.error('Notification permission denied');
      }
      
      return granted;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }, []);

  const unreadCount = notifications.filter(n => !n.read).length;

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    requestPermission,
    isPermissionGranted
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

// Hook for specific notification types
export const useNotificationHelpers = () => {
  const { addNotification } = useNotifications();

  const notifySuccess = (title: string, message: string, data?: Record<string, any>) => {
    addNotification({ title, message, type: 'success', data });
  };

  const notifyError = (title: string, message: string, data?: Record<string, any>) => {
    addNotification({ title, message, type: 'error', data });
  };

  const notifyWarning = (title: string, message: string, data?: Record<string, any>) => {
    addNotification({ title, message, type: 'warning', data });
  };

  const notifyInfo = (title: string, message: string, data?: Record<string, any>) => {
    addNotification({ title, message, type: 'info', data });
  };

  const notifyTranscriptionComplete = (noteId: string, noteTitle: string) => {
    addNotification({
      title: 'Transcription Complete',
      message: `"${noteTitle}" has been transcribed successfully`,
      type: 'success',
      data: { noteId, type: 'transcription_complete' },
      actions: [
        {
          id: 'view_note',
          label: 'View Note',
          action: () => window.location.href = `/notes/${noteId}`,
          style: 'primary'
        }
      ]
    });
  };

  const notifyAnalysisComplete = (noteId: string, noteTitle: string) => {
    addNotification({
      title: 'AI Analysis Complete',
      message: `Analysis for "${noteTitle}" is ready`,
      type: 'success',
      data: { noteId, type: 'analysis_complete' },
      actions: [
        {
          id: 'view_analysis',
          label: 'View Analysis',
          action: () => window.location.href = `/notes/${noteId}#analysis`,
          style: 'primary'
        }
      ]
    });
  };

  const notifyProcessingError = (noteTitle: string, error: string) => {
    addNotification({
      title: 'Processing Failed',
      message: `Failed to process "${noteTitle}": ${error}`,
      type: 'error',
      data: { type: 'processing_error', error }
    });
  };

  const notifyStorageLimitWarning = (usagePercentage: number) => {
    addNotification({
      title: 'Storage Limit Warning',
      message: `You've used ${usagePercentage}% of your storage limit`,
      type: 'warning',
      data: { type: 'storage_warning', usagePercentage },
      actions: [
        {
          id: 'upgrade_plan',
          label: 'Upgrade Plan',
          action: () => window.location.href = '/settings/billing',
          style: 'primary'
        }
      ]
    });
  };

  const notifyCollaboratorJoined = (noteId: string, collaboratorName: string) => {
    addNotification({
      title: 'Collaborator Joined',
      message: `${collaboratorName} joined your note`,
      type: 'info',
      data: { noteId, type: 'collaborator_joined', collaboratorName }
    });
  };

  return {
    notifySuccess,
    notifyError,
    notifyWarning,
    notifyInfo,
    notifyTranscriptionComplete,
    notifyAnalysisComplete,
    notifyProcessingError,
    notifyStorageLimitWarning,
    notifyCollaboratorJoined
  };
};

// Hook for notification settings
export const useNotificationSettings = () => {
  const { isPermissionGranted, requestPermission } = useNotifications();
  const [settings, setSettings] = useState({
    browser: isPermissionGranted,
    email: true,
    transcriptionComplete: true,
    analysisComplete: true,
    collaboratorActivity: true,
    storageWarnings: true
  });

  useEffect(() => {
    // Load settings from localStorage
    try {
      const saved = localStorage.getItem('quickcapt_notification_settings');
      if (saved) {
        setSettings(prev => ({ ...prev, ...JSON.parse(saved) }));
      }
    } catch (error) {
      console.error('Failed to load notification settings:', error);
    }
  }, []);

  const updateSettings = (newSettings: Partial<typeof settings>) => {
    const updated = { ...settings, ...newSettings };
    setSettings(updated);
    
    try {
      localStorage.setItem('quickcapt_notification_settings', JSON.stringify(updated));
    } catch (error) {
      console.error('Failed to save notification settings:', error);
    }
  };

  const enableBrowserNotifications = async () => {
    const granted = await requestPermission();
    if (granted) {
      updateSettings({ browser: true });
    }
    return granted;
  };

  return {
    settings,
    updateSettings,
    enableBrowserNotifications,
    isPermissionGranted
  };
};

export default useNotifications;
