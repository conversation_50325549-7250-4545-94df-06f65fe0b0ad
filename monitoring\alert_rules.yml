# Prometheus alerting rules for QuickCapt

groups:
  # Application Health Alerts
  - name: quickcapt.health
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
          description: "{{ $labels.job }} on {{ $labels.instance }} has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second on {{ $labels.instance }}"

      - alert: APIResponseTimeHigh
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "API response time is high"
          description: "95th percentile response time is {{ $value }}s on {{ $labels.instance }}"

  # Resource Usage Alerts
  - name: quickcapt.resources
    rules:
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Disk space is running low"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighDiskIOWait
        expr: irate(node_cpu_seconds_total{mode="iowait"}[5m]) * 100 > 20
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High disk I/O wait time"
          description: "I/O wait time is {{ $value }}% on {{ $labels.instance }}"

  # Database Alerts
  - name: quickcapt.database
    rules:
      - alert: PostgreSQLDown
        expr: pg_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database on {{ $labels.instance }} is down"

      - alert: PostgreSQLTooManyConnections
        expr: sum by (instance) (pg_stat_activity_count) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL has too many connections"
          description: "PostgreSQL has {{ $value }} connections on {{ $labels.instance }}"

      - alert: PostgreSQLSlowQueries
        expr: rate(pg_stat_activity_max_tx_duration[5m]) > 300
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL has slow queries"
          description: "PostgreSQL has queries running for more than 5 minutes on {{ $labels.instance }}"

      - alert: RedisDown
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis server on {{ $labels.instance }} is down"

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis memory usage is high"
          description: "Redis memory usage is {{ $value }}% on {{ $labels.instance }}"

  # Application-Specific Alerts
  - name: quickcapt.application
    rules:
      - alert: TranscriptionQueueBacklog
        expr: transcription_queue_size > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Transcription queue has a backlog"
          description: "Transcription queue has {{ $value }} pending jobs"

      - alert: AIServiceResponseTimeHigh
        expr: histogram_quantile(0.95, rate(ai_service_request_duration_seconds_bucket[5m])) > 30
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "AI service response time is high"
          description: "95th percentile AI service response time is {{ $value }}s"

      - alert: HighAudioProcessingFailureRate
        expr: rate(audio_processing_failures_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High audio processing failure rate"
          description: "Audio processing failure rate is {{ $value }} failures per second"

      - alert: UserRegistrationSpike
        expr: rate(user_registrations_total[5m]) > 10
        for: 2m
        labels:
          severity: info
        annotations:
          summary: "User registration spike detected"
          description: "User registration rate is {{ $value }} registrations per second"

      - alert: StorageQuotaExceeded
        expr: user_storage_usage_bytes / user_storage_quota_bytes > 0.95
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "User storage quota nearly exceeded"
          description: "User {{ $labels.user_id }} has used {{ $value }}% of their storage quota"

  # Security Alerts
  - name: quickcapt.security
    rules:
      - alert: HighFailedLoginRate
        expr: rate(failed_login_attempts_total[5m]) > 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High failed login rate detected"
          description: "Failed login rate is {{ $value }} attempts per second"

      - alert: SuspiciousAPIActivity
        expr: rate(http_requests_total{status="401"}[5m]) > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Suspicious API activity detected"
          description: "High rate of 401 responses: {{ $value }} per second"

      - alert: RateLimitExceeded
        expr: rate(rate_limit_exceeded_total[5m]) > 1
        for: 2m
        labels:
          severity: info
        annotations:
          summary: "Rate limit exceeded frequently"
          description: "Rate limit exceeded {{ $value }} times per second"

  # Business Metrics Alerts
  - name: quickcapt.business
    rules:
      - alert: LowActiveUsers
        expr: active_users_count < 100
        for: 30m
        labels:
          severity: info
        annotations:
          summary: "Low active user count"
          description: "Active user count is {{ $value }}, which is below normal levels"

      - alert: HighChurnRate
        expr: user_churn_rate > 0.1
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "High user churn rate"
          description: "User churn rate is {{ $value }}, which is above normal levels"

      - alert: RevenueDropAlert
        expr: daily_revenue < 1000
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "Daily revenue below threshold"
          description: "Daily revenue is ${{ $value }}, which is below the expected threshold"
