import rateLimit from 'express-rate-limit';
import RedisStore from 'rate-limit-redis';
import { Redis } from 'ioredis';
import { Request, Response } from 'express';
import { logger } from '../utils/logger';

// Redis client for rate limiting
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

// Custom key generator for rate limiting
const keyGenerator = (req: Request): string => {
  // Use user ID if authenticated, otherwise use IP
  const userId = req.user?.id;
  const ip = req.ip || req.connection.remoteAddress || 'unknown';
  return userId ? `user:${userId}` : `ip:${ip}`;
};

// Custom handler for rate limit exceeded
const rateLimitHandler = (req: Request, res: Response): void => {
  const identifier = keyGenerator(req);
  logger.warn(`Rate limit exceeded for ${identifier}`, {
    ip: req.ip,
    userId: req.user?.id,
    path: req.path,
    method: req.method
  });

  res.status(429).json({
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests. Please try again later.',
      retryAfter: Math.ceil(60) // seconds
    },
    timestamp: new Date().toISOString()
  });
};

// General API rate limiter
export const generalRateLimit = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Limit each user/IP to 1000 requests per windowMs
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
});

// Strict rate limiter for authentication endpoints
export const authRateLimit = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 auth requests per windowMs
  keyGenerator: (req: Request) => req.ip || 'unknown',
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
});

// Audio upload rate limiter
export const audioUploadRateLimit = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50, // Limit each user to 50 audio uploads per hour
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
});

// AI processing rate limiter
export const aiProcessingRateLimit = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 100, // Limit each user to 100 AI processing requests per hour
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
});

// Search rate limiter
export const searchRateLimit = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 60 * 1000, // 1 minute
  max: 60, // Limit each user to 60 search requests per minute
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
});

// WebSocket connection rate limiter
export const websocketRateLimit = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 60 * 1000, // 1 minute
  max: 10, // Limit each user to 10 WebSocket connections per minute
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
});

// Premium user rate limiter (higher limits)
export const premiumRateLimit = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5000, // Higher limit for premium users
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req: Request) => {
    // Skip rate limiting for premium users
    return req.user?.subscriptionType === 'PREMIUM' || req.user?.subscriptionType === 'ENTERPRISE';
  }
});

// Dynamic rate limiter based on subscription type
export const subscriptionBasedRateLimit = (req: Request, res: Response, next: any) => {
  const user = req.user;
  
  if (!user) {
    // Apply strict limits for unauthenticated users
    return generalRateLimit(req, res, next);
  }

  switch (user.subscriptionType) {
    case 'ENTERPRISE':
      // No rate limiting for enterprise users
      return next();
    case 'PREMIUM':
      return premiumRateLimit(req, res, next);
    case 'FREE':
    default:
      return generalRateLimit(req, res, next);
  }
};

// Rate limiter middleware factory
export const createRateLimit = (options: {
  windowMs: number;
  max: number;
  message?: string;
  skipSuccessfulRequests?: boolean;
}) => {
  return rateLimit({
    store: new RedisStore({
      sendCommand: (...args: string[]) => redis.call(...args),
    }),
    windowMs: options.windowMs,
    max: options.max,
    keyGenerator,
    handler: (req: Request, res: Response) => {
      const identifier = keyGenerator(req);
      logger.warn(`Custom rate limit exceeded for ${identifier}`, {
        ip: req.ip,
        userId: req.user?.id,
        path: req.path,
        method: req.method,
        limit: options.max,
        window: options.windowMs
      });

      res.status(429).json({
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: options.message || 'Too many requests. Please try again later.',
          retryAfter: Math.ceil(options.windowMs / 1000)
        },
        timestamp: new Date().toISOString()
      });
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: options.skipSuccessfulRequests || false
  });
};

export const rateLimiterMiddleware = {
  general: generalRateLimit,
  auth: authRateLimit,
  audioUpload: audioUploadRateLimit,
  aiProcessing: aiProcessingRateLimit,
  search: searchRateLimit,
  websocket: websocketRateLimit,
  premium: premiumRateLimit,
  subscriptionBased: subscriptionBasedRateLimit,
  create: createRateLimit
};
