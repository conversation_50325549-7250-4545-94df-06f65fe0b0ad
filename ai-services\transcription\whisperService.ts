import OpenAI from 'openai';
import fs from 'fs';
import path from 'path';
import ffmpeg from 'fluent-ffmpeg';
import { v4 as uuidv4 } from 'uuid';
import tmp from 'tmp';

interface TranscriptionOptions {
  language?: string;
  model?: 'whisper-1';
  response_format?: 'json' | 'text' | 'srt' | 'verbose_json' | 'vtt';
  temperature?: number;
  timestamp_granularities?: ('word' | 'segment')[];
}

interface TranscriptionSegment {
  id: number;
  seek: number;
  start: number;
  end: number;
  text: string;
  tokens: number[];
  temperature: number;
  avg_logprob: number;
  compression_ratio: number;
  no_speech_prob: number;
}

interface TranscriptionWord {
  word: string;
  start: number;
  end: number;
}

interface TranscriptionResult {
  text: string;
  segments?: TranscriptionSegment[];
  words?: TranscriptionWord[];
  language?: string;
  duration?: number;
}

export class WhisperService {
  private openai: OpenAI;
  private tempDir: string;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    
    // Create temporary directory for audio processing
    this.tempDir = tmp.dirSync({ prefix: 'quickcapt-audio-' }).name;
  }

  /**
   * Transcribe audio file using OpenAI Whisper
   */
  async transcribeAudio(
    audioBuffer: Buffer,
    filename: string,
    options: TranscriptionOptions = {}
  ): Promise<TranscriptionResult> {
    try {
      // Prepare audio file
      const processedAudioPath = await this.prepareAudioFile(audioBuffer, filename);
      
      // Set default options
      const transcriptionOptions: TranscriptionOptions = {
        model: 'whisper-1',
        response_format: 'verbose_json',
        timestamp_granularities: ['word', 'segment'],
        temperature: 0,
        ...options
      };

      // Create file stream for OpenAI API
      const audioFile = fs.createReadStream(processedAudioPath);
      
      // Call OpenAI Whisper API
      const response = await this.openai.audio.transcriptions.create({
        file: audioFile,
        ...transcriptionOptions
      });

      // Clean up temporary file
      this.cleanupFile(processedAudioPath);

      // Process and return result
      return this.processTranscriptionResponse(response);
      
    } catch (error) {
      console.error('Whisper transcription error:', error);
      throw new Error(`Transcription failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Transcribe audio in real-time chunks
   */
  async transcribeChunk(
    audioChunk: Buffer,
    chunkIndex: number,
    options: TranscriptionOptions = {}
  ): Promise<{ text: string; isFinal: boolean }> {
    try {
      // For real-time transcription, we use smaller chunks
      const chunkPath = await this.prepareAudioChunk(audioChunk, chunkIndex);
      
      const audioFile = fs.createReadStream(chunkPath);
      
      const response = await this.openai.audio.transcriptions.create({
        file: audioFile,
        model: 'whisper-1',
        response_format: 'text',
        temperature: 0.2, // Slightly higher for real-time
        ...options
      });

      this.cleanupFile(chunkPath);

      return {
        text: typeof response === 'string' ? response : response.text,
        isFinal: true // Whisper always returns final results
      };
      
    } catch (error) {
      console.error('Chunk transcription error:', error);
      return {
        text: '',
        isFinal: false
      };
    }
  }

  /**
   * Prepare audio file for transcription
   */
  private async prepareAudioFile(audioBuffer: Buffer, filename: string): Promise<string> {
    const inputPath = path.join(this.tempDir, `input_${uuidv4()}_${filename}`);
    const outputPath = path.join(this.tempDir, `processed_${uuidv4()}.wav`);

    // Write buffer to temporary file
    fs.writeFileSync(inputPath, audioBuffer);

    // Convert to WAV format with optimal settings for Whisper
    await new Promise<void>((resolve, reject) => {
      ffmpeg(inputPath)
        .toFormat('wav')
        .audioChannels(1) // Mono
        .audioFrequency(16000) // 16kHz sample rate
        .audioBitrate('16k')
        .on('end', () => resolve())
        .on('error', (error) => reject(error))
        .save(outputPath);
    });

    // Clean up input file
    this.cleanupFile(inputPath);

    return outputPath;
  }

  /**
   * Prepare audio chunk for real-time transcription
   */
  private async prepareAudioChunk(audioChunk: Buffer, chunkIndex: number): Promise<string> {
    const chunkPath = path.join(this.tempDir, `chunk_${chunkIndex}_${uuidv4()}.wav`);

    // For chunks, we assume they're already in a suitable format
    // In a real implementation, you might want to do minimal processing
    fs.writeFileSync(chunkPath, audioChunk);

    return chunkPath;
  }

  /**
   * Process transcription response from OpenAI
   */
  private processTranscriptionResponse(response: any): TranscriptionResult {
    if (typeof response === 'string') {
      return { text: response };
    }

    const result: TranscriptionResult = {
      text: response.text,
      language: response.language,
      duration: response.duration
    };

    // Add segments if available
    if (response.segments) {
      result.segments = response.segments.map((segment: any) => ({
        id: segment.id,
        seek: segment.seek,
        start: segment.start,
        end: segment.end,
        text: segment.text,
        tokens: segment.tokens || [],
        temperature: segment.temperature || 0,
        avg_logprob: segment.avg_logprob || 0,
        compression_ratio: segment.compression_ratio || 0,
        no_speech_prob: segment.no_speech_prob || 0
      }));
    }

    // Add words if available
    if (response.words) {
      result.words = response.words.map((word: any) => ({
        word: word.word,
        start: word.start,
        end: word.end
      }));
    }

    return result;
  }

  /**
   * Get supported languages
   */
  getSupportedLanguages(): string[] {
    return [
      'en', 'zh', 'de', 'es', 'ru', 'ko', 'fr', 'ja', 'pt', 'tr', 'pl', 'ca', 'nl',
      'ar', 'sv', 'it', 'id', 'hi', 'fi', 'vi', 'he', 'uk', 'el', 'ms', 'cs', 'ro',
      'da', 'hu', 'ta', 'no', 'th', 'ur', 'hr', 'bg', 'lt', 'la', 'mi', 'ml', 'cy',
      'sk', 'te', 'fa', 'lv', 'bn', 'sr', 'az', 'sl', 'kn', 'et', 'mk', 'br', 'eu',
      'is', 'hy', 'ne', 'mn', 'bs', 'kk', 'sq', 'sw', 'gl', 'mr', 'pa', 'si', 'km',
      'sn', 'yo', 'so', 'af', 'oc', 'ka', 'be', 'tg', 'sd', 'gu', 'am', 'yi', 'lo',
      'uz', 'fo', 'ht', 'ps', 'tk', 'nn', 'mt', 'sa', 'lb', 'my', 'bo', 'tl', 'mg',
      'as', 'tt', 'haw', 'ln', 'ha', 'ba', 'jw', 'su'
    ];
  }

  /**
   * Detect language from audio
   */
  async detectLanguage(audioBuffer: Buffer, filename: string): Promise<string> {
    try {
      const processedAudioPath = await this.prepareAudioFile(audioBuffer, filename);
      const audioFile = fs.createReadStream(processedAudioPath);
      
      // Use a small portion for language detection
      const response = await this.openai.audio.transcriptions.create({
        file: audioFile,
        model: 'whisper-1',
        response_format: 'verbose_json'
      });

      this.cleanupFile(processedAudioPath);

      return typeof response === 'object' && response.language ? response.language : 'en';
      
    } catch (error) {
      console.error('Language detection error:', error);
      return 'en'; // Default to English
    }
  }

  /**
   * Clean up temporary files
   */
  private cleanupFile(filePath: string): void {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      console.error('File cleanup error:', error);
    }
  }

  /**
   * Clean up temporary directory
   */
  cleanup(): void {
    try {
      if (fs.existsSync(this.tempDir)) {
        fs.rmSync(this.tempDir, { recursive: true, force: true });
      }
    } catch (error) {
      console.error('Directory cleanup error:', error);
    }
  }
}

export default WhisperService;
