import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import {
  DocumentTextIcon,
  TagIcon,
  BookmarkIcon,
  ShareIcon,
  TrashIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { BookmarkIcon as BookmarkSolidIcon } from '@heroicons/react/24/solid';
import toast from 'react-hot-toast';

import { Note, NoteCategory } from '../../types';
import { useNotes } from '../../hooks/useNotes';

interface NoteEditorProps {
  note?: Note;
  onSave?: (note: Note) => void;
  onCancel?: () => void;
  onDelete?: (noteId: string) => void;
  className?: string;
}

const categories: { value: NoteCategory; label: string; color: string }[] = [
  { value: 'MEETING', label: 'Meeting', color: 'bg-blue-100 text-blue-800' },
  { value: 'LECTURE', label: 'Lecture', color: 'bg-green-100 text-green-800' },
  { value: 'INTERVIEW', label: 'Interview', color: 'bg-purple-100 text-purple-800' },
  { value: 'PERSONAL', label: 'Personal', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'OTHER', label: 'Other', color: 'bg-gray-100 text-gray-800' }
];

const NoteEditor: React.FC<NoteEditorProps> = ({
  note,
  onSave,
  onCancel,
  onDelete,
  className = ''
}) => {
  const { createNote, updateNote, deleteNote, toggleFavorite } = useNotes();
  
  const [title, setTitle] = useState(note?.title || '');
  const [content, setContent] = useState(note?.content || '');
  const [category, setCategory] = useState<NoteCategory>(note?.category || 'OTHER');
  const [tags, setTags] = useState<string[]>(note?.tags || []);
  const [newTag, setNewTag] = useState('');
  const [isPublic, setIsPublic] = useState(note?.isPublic || false);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const titleRef = useRef<HTMLInputElement>(null);
  const contentRef = useRef<HTMLTextAreaElement>(null);

  // Track changes
  useEffect(() => {
    const hasContentChanges = 
      title !== (note?.title || '') ||
      content !== (note?.content || '') ||
      category !== (note?.category || 'OTHER') ||
      JSON.stringify(tags) !== JSON.stringify(note?.tags || []) ||
      isPublic !== (note?.isPublic || false);
    
    setHasChanges(hasContentChanges);
  }, [title, content, category, tags, isPublic, note]);

  // Auto-focus title for new notes
  useEffect(() => {
    if (!note && titleRef.current) {
      titleRef.current.focus();
    }
  }, [note]);

  // Auto-resize textarea
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.style.height = 'auto';
      contentRef.current.style.height = `${contentRef.current.scrollHeight}px`;
    }
  }, [content]);

  const handleSave = async () => {
    if (!title.trim()) {
      toast.error('Please enter a title');
      titleRef.current?.focus();
      return;
    }

    setIsSaving(true);
    try {
      const noteData = {
        title: title.trim(),
        content: content.trim(),
        category,
        tags,
        isPublic
      };

      let savedNote: Note;
      if (note) {
        savedNote = await updateNote(note.id, noteData);
        toast.success('Note updated successfully');
      } else {
        savedNote = await createNote(noteData);
        toast.success('Note created successfully');
      }

      setHasChanges(false);
      onSave?.(savedNote);
    } catch (error) {
      toast.error('Failed to save note');
      console.error('Save error:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!note) return;

    if (window.confirm('Are you sure you want to delete this note?')) {
      try {
        await deleteNote(note.id);
        toast.success('Note deleted successfully');
        onDelete?.(note.id);
      } catch (error) {
        toast.error('Failed to delete note');
        console.error('Delete error:', error);
      }
    }
  };

  const handleToggleFavorite = async () => {
    if (!note) return;

    try {
      await toggleFavorite(note.id);
      toast.success(note.isFavorite ? 'Removed from favorites' : 'Added to favorites');
    } catch (error) {
      toast.error('Failed to update favorite status');
      console.error('Toggle favorite error:', error);
    }
  };

  const handleAddTag = () => {
    const tag = newTag.trim();
    if (tag && !tags.includes(tag)) {
      setTags([...tags, tag]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      handleSave();
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <DocumentTextIcon className="h-5 w-5 text-gray-400" />
          <span className="text-sm font-medium text-gray-700">
            {note ? 'Edit Note' : 'New Note'}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          {note && (
            <button
              onClick={handleToggleFavorite}
              className="p-2 text-gray-400 hover:text-yellow-500 transition-colors"
              title={note.isFavorite ? 'Remove from favorites' : 'Add to favorites'}
            >
              {note.isFavorite ? (
                <BookmarkSolidIcon className="h-5 w-5 text-yellow-500" />
              ) : (
                <BookmarkIcon className="h-5 w-5" />
              )}
            </button>
          )}
          
          <button
            onClick={() => setIsPublic(!isPublic)}
            className={`p-2 transition-colors ${
              isPublic 
                ? 'text-blue-500 hover:text-blue-600' 
                : 'text-gray-400 hover:text-gray-500'
            }`}
            title={isPublic ? 'Make private' : 'Make public'}
          >
            <ShareIcon className="h-5 w-5" />
          </button>
          
          {note && (
            <button
              onClick={handleDelete}
              className="p-2 text-gray-400 hover:text-red-500 transition-colors"
              title="Delete note"
            >
              <TrashIcon className="h-5 w-5" />
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-4 space-y-4">
        {/* Title */}
        <input
          ref={titleRef}
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Enter note title..."
          className="w-full text-xl font-semibold text-gray-900 placeholder-gray-400 border-none outline-none resize-none"
        />

        {/* Category and Tags */}
        <div className="flex flex-wrap items-center gap-3">
          <select
            value={category}
            onChange={(e) => setCategory(e.target.value as NoteCategory)}
            className="px-3 py-1 text-sm rounded-full border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {categories.map(cat => (
              <option key={cat.value} value={cat.value}>
                {cat.label}
              </option>
            ))}
          </select>

          {tags.map(tag => (
            <span
              key={tag}
              className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
            >
              <TagIcon className="h-3 w-3 mr-1" />
              {tag}
              <button
                onClick={() => handleRemoveTag(tag)}
                className="ml-1 text-blue-600 hover:text-blue-800"
              >
                <XMarkIcon className="h-3 w-3" />
              </button>
            </span>
          ))}

          <div className="flex items-center">
            <input
              type="text"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
              placeholder="Add tag..."
              className="px-2 py-1 text-xs border border-gray-200 rounded-l focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={handleAddTag}
              className="px-2 py-1 text-xs bg-blue-500 text-white rounded-r hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Add
            </button>
          </div>
        </div>

        {/* Content */}
        <textarea
          ref={contentRef}
          value={content}
          onChange={(e) => setContent(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Start writing your note..."
          className="w-full min-h-[200px] text-gray-900 placeholder-gray-400 border-none outline-none resize-none"
        />
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          {hasChanges && <span className="text-orange-500">Unsaved changes</span>}
          {note && (
            <span>
              Last updated: {new Date(note.updatedAt).toLocaleDateString()}
            </span>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving || !hasChanges}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Saving...
              </>
            ) : (
              <>
                <CheckIcon className="h-4 w-4 mr-2" />
                {note ? 'Update' : 'Create'}
              </>
            )}
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default NoteEditor;
