// Prisma schema for QuickCapt database

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  avatar    String?
  status    UserStatus @default(ACTIVE)
  
  // Preferences
  preferences Json @default("{}")
  
  // Subscription
  subscriptionType SubscriptionType @default(FREE)
  subscriptionStatus SubscriptionStatus @default(ACTIVE)
  subscriptionExpiresAt DateTime?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastLoginAt DateTime?
  emailVerifiedAt DateTime?
  
  // Relations
  notes Note[]
  audioFiles AudioFile[]
  collaborations Collaboration[]
  apiKeys ApiKey[]
  sessions Session[]
  usageRecords UsageRecord[]
  
  @@map("users")
}

model Note {
  id       String @id @default(cuid())
  title    String
  content  String @default("")
  summary  String?
  category NoteCategory @default(OTHER)
  tags     String[]
  
  // Status
  isPublic Boolean @default(false)
  isArchived Boolean @default(false)
  isFavorite Boolean @default(false)
  
  // Audio
  audioFileId String?
  audioFile   AudioFile? @relation(fields: [audioFileId], references: [id])
  
  // AI Analysis
  transcriptionId String?
  transcription   Transcription? @relation(fields: [transcriptionId], references: [id])
  analysisId      String?
  analysis        Analysis? @relation(fields: [analysisId], references: [id])
  
  // Metadata
  metadata Json @default("{}")
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  userId String
  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)
  collaborations Collaboration[]
  
  @@map("notes")
  @@index([userId, createdAt])
  @@index([category])
  @@index([isPublic])
}

model AudioFile {
  id           String @id @default(cuid())
  filename     String
  originalName String
  mimeType     String
  size         Int
  duration     Float?
  sampleRate   Int?
  channels     Int?
  url          String
  waveformData Json?
  
  // Processing status
  processingStatus ProcessingStatus @default(PENDING)
  processingError  String?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  userId String
  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)
  notes  Note[]
  transcriptions Transcription[]
  
  @@map("audio_files")
  @@index([userId, createdAt])
}

model Transcription {
  id       String @id @default(cuid())
  text     String
  language String @default("en")
  confidence Float @default(0.0)
  
  // Processing
  processingStatus ProcessingStatus @default(PENDING)
  processingError  String?
  
  // Segments and speakers
  segments Json @default("[]")
  speakers Json @default("[]")
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  audioFileId String
  audioFile   AudioFile @relation(fields: [audioFileId], references: [id], onDelete: Cascade)
  notes       Note[]
  
  @@map("transcriptions")
  @@index([audioFileId])
}

model Analysis {
  id      String @id @default(cuid())
  summary String?
  
  // AI Analysis results
  keyPoints    Json @default("[]")
  actionItems  Json @default("[]")
  sentiment    Json @default("{}")
  topics       Json @default("[]")
  entities     Json @default("[]")
  decisions    Json @default("[]")
  questions    Json @default("[]")
  
  // Processing
  processingStatus ProcessingStatus @default(PENDING)
  processingError  String?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  notes Note[]
  
  @@map("analyses")
}

model Collaboration {
  id   String @id @default(cuid())
  role CollaborationRole @default(VIEWER)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  userId String
  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)
  noteId String
  note   Note @relation(fields: [noteId], references: [id], onDelete: Cascade)
  
  @@unique([userId, noteId])
  @@map("collaborations")
}

model Session {
  id        String @id @default(cuid())
  token     String @unique
  expiresAt DateTime
  
  // Device info
  userAgent String?
  ipAddress String?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  userId String
  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("sessions")
  @@index([userId])
  @@index([expiresAt])
}

model ApiKey {
  id          String @id @default(cuid())
  name        String
  key         String @unique
  permissions String[]
  isActive    Boolean @default(true)
  lastUsedAt  DateTime?
  expiresAt   DateTime?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  userId String
  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("api_keys")
  @@index([userId])
}

model UsageRecord {
  id     String @id @default(cuid())
  type   UsageType
  amount Float
  unit   String
  
  // Metadata
  metadata Json @default("{}")
  
  // Timestamps
  createdAt DateTime @default(now())
  
  // Relations
  userId String
  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("usage_records")
  @@index([userId, createdAt])
  @@index([type, createdAt])
}

model Subscription {
  id     String @id @default(cuid())
  type   SubscriptionType
  status SubscriptionStatus
  
  // Billing
  stripeCustomerId     String?
  stripeSubscriptionId String?
  currentPeriodStart   DateTime?
  currentPeriodEnd     DateTime?
  
  // Features and limits
  features Json @default("[]")
  limits   Json @default("{}")
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("subscriptions")
}

model Webhook {
  id        String @id @default(cuid())
  provider  String
  eventType String
  payload   Json
  processed Boolean @default(false)
  
  // Timestamps
  createdAt DateTime @default(now())
  
  @@map("webhooks")
  @@index([provider, eventType])
  @@index([processed, createdAt])
}

// Enums
enum UserStatus {
  ACTIVE
  SUSPENDED
  DELETED
}

enum SubscriptionType {
  FREE
  PRO
  TEAM
  ENTERPRISE
}

enum SubscriptionStatus {
  ACTIVE
  CANCELLED
  EXPIRED
  PAST_DUE
}

enum NoteCategory {
  MEETING
  LECTURE
  INTERVIEW
  BRAINSTORM
  CALL
  PRESENTATION
  OTHER
}

enum ProcessingStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

enum CollaborationRole {
  VIEWER
  EDITOR
  ADMIN
}

enum UsageType {
  TRANSCRIPTION_MINUTES
  STORAGE_BYTES
  API_REQUESTS
  AI_ANALYSIS
}
