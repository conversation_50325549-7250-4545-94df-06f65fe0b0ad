import { Router } from 'express';
import authRoutes from './auth';
import notesRoutes from './notes';
import audioRoutes from './audio';
import transcriptionRoutes from './transcription';
import analysisRoutes from './analysis';
import searchRoutes from './search';
import userRoutes from './user';
import subscriptionRoutes from './subscription';
import webhookRoutes from './webhook';
import healthRoutes from './health';
import { authenticate, optionalAuth } from '../middleware/auth';
import { rateLimitMiddleware } from '../middleware/rateLimit';

const router = Router();

// Health check routes (no auth required)
router.use('/health', healthRoutes);

// Webhook routes (no auth required, but verified)
router.use('/webhooks', webhookRoutes);

// Authentication routes (no auth required)
router.use('/auth', rateLimitMiddleware('auth'), authRoutes);

// Public routes with optional auth
router.use('/search', optionalAuth, rateLimitMiddleware('search'), searchRoutes);

// Protected routes (authentication required)
router.use('/users', authenticate, rateLimitMiddleware('general'), userRoutes);
router.use('/notes', authenticate, rateLimitMiddleware('general'), notesRoutes);
router.use('/audio', authenticate, rateLimitMiddleware('upload'), audioRoutes);
router.use('/transcription', authenticate, rateLimitMiddleware('transcription'), transcriptionRoutes);
router.use('/analysis', authenticate, rateLimitMiddleware('general'), analysisRoutes);
router.use('/subscription', authenticate, rateLimitMiddleware('general'), subscriptionRoutes);

// API documentation route
router.get('/docs', (req, res) => {
  res.redirect('/api-docs');
});

// API version info
router.get('/', (req, res) => {
  res.json({
    name: 'QuickCapt API',
    version: '1.0.0',
    description: 'AI-Powered Note Taking API',
    documentation: '/api-docs',
    health: '/api/health',
    timestamp: new Date().toISOString()
  });
});

// 404 handler for API routes
router.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `API endpoint ${req.originalUrl} not found`
    },
    timestamp: new Date().toISOString()
  });
});

export default router;
