import { Router } from 'express';
import authRoutes from './auth';
import notesRoutes from './notes';
import audioRoutes from './audio';
import transcriptionRoutes from './transcription';
import analysisRoutes from './analysis';
import searchRoutes from './search';
import userRoutes from './user';
import subscriptionRoutes from './subscription';
import webhookRoutes from './webhook';
import healthRoutes from './health';
import { authMiddleware } from '../middleware/auth';
import { rateLimiterMiddleware } from '../middleware/rateLimiter';

const router = Router();

// Health check routes (no auth required)
router.use('/health', healthRoutes);

// Webhook routes (no auth required, but verified)
router.use('/webhooks', webhookRoutes);

// Authentication routes (no auth required)
router.use('/auth', rateLimiterMiddleware.auth, authRoutes);

// Public routes with optional auth
router.use('/search', authMiddleware.optionalAuth, rateLimiterMiddleware.search, searchRoutes);

// Protected routes (authentication required)
router.use('/users', authMiddleware.requireAuth, rateLimiterMiddleware.general, userRoutes);
router.use('/notes', authMiddleware.requireAuth, rateLimiterMiddleware.general, notesRoutes);
router.use('/audio', authMiddleware.requireAuth, rateLimiterMiddleware.audioUpload, audioRoutes);
router.use('/transcription', authMiddleware.requireAuth, rateLimiterMiddleware.aiProcessing, transcriptionRoutes);
router.use('/analysis', authMiddleware.requireAuth, rateLimiterMiddleware.general, analysisRoutes);
router.use('/subscription', authMiddleware.requireAuth, rateLimiterMiddleware.general, subscriptionRoutes);

// API documentation route
router.get('/docs', (req, res) => {
  res.redirect('/api-docs');
});

// API version info
router.get('/', (req, res) => {
  res.json({
    name: 'QuickCapt API',
    version: '1.0.0',
    description: 'AI-Powered Note Taking API',
    documentation: '/api-docs',
    health: '/api/health',
    timestamp: new Date().toISOString()
  });
});

// 404 handler for API routes
router.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `API endpoint ${req.originalUrl} not found`
    },
    timestamp: new Date().toISOString()
  });
});

export default router;
