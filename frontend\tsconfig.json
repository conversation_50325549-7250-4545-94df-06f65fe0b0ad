{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6", "webworker"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/hooks/*": ["hooks/*"], "@/services/*": ["services/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"]}}, "include": ["src", "public/sw.js"]}