import Redis, { RedisOptions } from 'ioredis';
import { logger } from '../utils/logger';

interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  keyPrefix: string;
  maxRetriesPerRequest: number;
  retryDelayOnFailover: number;
  enableReadyCheck: boolean;
  maxRetriesPerRequest: number;
  lazyConnect: boolean;
}

class RedisConnection {
  private client: Redis | null = null;
  private subscriber: Redis | null = null;
  private publisher: Redis | null = null;
  private config: RedisConfig;

  constructor() {
    this.config = this.loadConfig();
  }

  private loadConfig(): RedisConfig {
    const redisUrl = process.env.REDIS_URL;
    
    if (redisUrl) {
      // Parse REDIS_URL (common in cloud deployments)
      const url = new URL(redisUrl);
      return {
        host: url.hostname,
        port: parseInt(url.port) || 6379,
        password: url.password || undefined,
        db: parseInt(url.pathname.slice(1)) || 0,
        keyPrefix: process.env.REDIS_KEY_PREFIX || 'quickcapt:',
        maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES || '3'),
        retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY || '100'),
        enableReadyCheck: true,
        lazyConnect: true
      };
    }

    // Fallback to individual environment variables
    return {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD || undefined,
      db: parseInt(process.env.REDIS_DB || '0'),
      keyPrefix: process.env.REDIS_KEY_PREFIX || 'quickcapt:',
      maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES || '3'),
      retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY || '100'),
      enableReadyCheck: true,
      lazyConnect: true
    };
  }

  public async connect(): Promise<void> {
    try {
      const redisOptions: RedisOptions = {
        host: this.config.host,
        port: this.config.port,
        password: this.config.password,
        db: this.config.db,
        keyPrefix: this.config.keyPrefix,
        maxRetriesPerRequest: this.config.maxRetriesPerRequest,
        retryDelayOnFailover: this.config.retryDelayOnFailover,
        enableReadyCheck: this.config.enableReadyCheck,
        lazyConnect: this.config.lazyConnect,
        // Connection timeout
        connectTimeout: 10000,
        // Command timeout
        commandTimeout: 5000,
        // Retry strategy
        retryStrategy: (times: number) => {
          const delay = Math.min(times * 50, 2000);
          logger.warn(`Redis connection retry attempt ${times}, delay: ${delay}ms`);
          return delay;
        },
        // Reconnect on error
        reconnectOnError: (err: Error) => {
          const targetError = 'READONLY';
          return err.message.includes(targetError);
        },
        // Family preference (IPv4)
        family: 4
      };

      // Create main client
      this.client = new Redis(redisOptions);

      // Create subscriber client (separate connection for pub/sub)
      this.subscriber = new Redis({
        ...redisOptions,
        keyPrefix: '' // No prefix for pub/sub
      });

      // Create publisher client
      this.publisher = new Redis({
        ...redisOptions,
        keyPrefix: '' // No prefix for pub/sub
      });

      // Set up event handlers for main client
      this.client.on('connect', () => {
        logger.info('Redis client connected');
      });

      this.client.on('ready', () => {
        logger.info('Redis client ready');
      });

      this.client.on('error', (error) => {
        logger.error('Redis client error:', error);
      });

      this.client.on('close', () => {
        logger.warn('Redis client connection closed');
      });

      this.client.on('reconnecting', () => {
        logger.info('Redis client reconnecting');
      });

      // Set up event handlers for subscriber
      this.subscriber.on('connect', () => {
        logger.info('Redis subscriber connected');
      });

      this.subscriber.on('error', (error) => {
        logger.error('Redis subscriber error:', error);
      });

      // Set up event handlers for publisher
      this.publisher.on('connect', () => {
        logger.info('Redis publisher connected');
      });

      this.publisher.on('error', (error) => {
        logger.error('Redis publisher error:', error);
      });

      // Test connections
      await this.client.ping();
      await this.subscriber.ping();
      await this.publisher.ping();

      logger.info(`Redis connected successfully to ${this.config.host}:${this.config.port}/${this.config.db}`);

    } catch (error) {
      logger.error('Redis connection failed:', error);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      if (this.client) {
        await this.client.quit();
        this.client = null;
      }
      if (this.subscriber) {
        await this.subscriber.quit();
        this.subscriber = null;
      }
      if (this.publisher) {
        await this.publisher.quit();
        this.publisher = null;
      }
      logger.info('Redis disconnected');
    } catch (error) {
      logger.error('Redis disconnect error:', error);
    }
  }

  public getClient(): Redis {
    if (!this.client) {
      throw new Error('Redis not connected. Call connect() first.');
    }
    return this.client;
  }

  public getSubscriber(): Redis {
    if (!this.subscriber) {
      throw new Error('Redis subscriber not connected. Call connect() first.');
    }
    return this.subscriber;
  }

  public getPublisher(): Redis {
    if (!this.publisher) {
      throw new Error('Redis publisher not connected. Call connect() first.');
    }
    return this.publisher;
  }

  // Convenience methods
  public async get(key: string): Promise<string | null> {
    return this.getClient().get(key);
  }

  public async set(key: string, value: string, ttl?: number): Promise<void> {
    if (ttl) {
      await this.getClient().setex(key, ttl, value);
    } else {
      await this.getClient().set(key, value);
    }
  }

  public async del(key: string): Promise<number> {
    return this.getClient().del(key);
  }

  public async exists(key: string): Promise<number> {
    return this.getClient().exists(key);
  }

  public async expire(key: string, seconds: number): Promise<number> {
    return this.getClient().expire(key, seconds);
  }

  public async ttl(key: string): Promise<number> {
    return this.getClient().ttl(key);
  }

  public async keys(pattern: string): Promise<string[]> {
    return this.getClient().keys(pattern);
  }

  public async hget(key: string, field: string): Promise<string | null> {
    return this.getClient().hget(key, field);
  }

  public async hset(key: string, field: string, value: string): Promise<number> {
    return this.getClient().hset(key, field, value);
  }

  public async hdel(key: string, field: string): Promise<number> {
    return this.getClient().hdel(key, field);
  }

  public async hgetall(key: string): Promise<Record<string, string>> {
    return this.getClient().hgetall(key);
  }

  // Pub/Sub methods
  public async publish(channel: string, message: string): Promise<number> {
    return this.getPublisher().publish(channel, message);
  }

  public async subscribe(channel: string, callback: (message: string) => void): Promise<void> {
    const subscriber = this.getSubscriber();
    subscriber.on('message', (receivedChannel, message) => {
      if (receivedChannel === channel) {
        callback(message);
      }
    });
    await subscriber.subscribe(channel);
  }

  public async unsubscribe(channel: string): Promise<void> {
    await this.getSubscriber().unsubscribe(channel);
  }

  // Health check
  public async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      connected: boolean;
      responseTime?: number;
      memoryUsage?: string;
      connectedClients?: number;
    };
  }> {
    try {
      if (!this.client) {
        return {
          status: 'unhealthy',
          details: {
            connected: false
          }
        };
      }

      const start = Date.now();
      await this.client.ping();
      const responseTime = Date.now() - start;

      // Get Redis info
      const info = await this.client.info('memory');
      const memoryMatch = info.match(/used_memory_human:(.+)/);
      const memoryUsage = memoryMatch ? memoryMatch[1].trim() : 'unknown';

      const clientsInfo = await this.client.info('clients');
      const clientsMatch = clientsInfo.match(/connected_clients:(\d+)/);
      const connectedClients = clientsMatch ? parseInt(clientsMatch[1]) : 0;

      return {
        status: 'healthy',
        details: {
          connected: true,
          responseTime,
          memoryUsage,
          connectedClients
        }
      };
    } catch (error) {
      logger.error('Redis health check failed:', error);
      return {
        status: 'unhealthy',
        details: {
          connected: false
        }
      };
    }
  }

  // Cache helper methods
  public async cache<T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl: number = 3600
  ): Promise<T> {
    try {
      // Try to get from cache
      const cached = await this.get(key);
      if (cached) {
        return JSON.parse(cached);
      }

      // Fetch data
      const data = await fetcher();

      // Store in cache
      await this.set(key, JSON.stringify(data), ttl);

      return data;
    } catch (error) {
      logger.error('Cache operation failed:', error);
      // Fallback to fetcher if cache fails
      return fetcher();
    }
  }

  public async invalidatePattern(pattern: string): Promise<number> {
    const keys = await this.keys(pattern);
    if (keys.length === 0) return 0;
    return this.getClient().del(...keys);
  }
}

// Create singleton instance
export const redisClient = new RedisConnection().getClient();
export const redisConfig = new RedisConnection();

// Export for use in other modules
export default redisConfig;
