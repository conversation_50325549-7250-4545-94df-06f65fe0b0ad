import { Request, Response, NextFunction } from 'express';
import { body, param, query, validationResult, ValidationChain } from 'express-validator';
import { ValidationError } from './errorHandler';

// Validation result handler
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.param,
      message: error.msg,
      value: error.value
    }));
    
    throw new ValidationError('Validation failed', errorMessages);
  }
  
  next();
};

// Common validation rules
export const commonValidations = {
  // ID validation
  id: param('id').isString().isLength({ min: 1 }).withMessage('Valid ID is required'),
  
  // Email validation
  email: body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  
  // Password validation
  password: body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character'),
  
  // Name validation
  name: body('name')
    .isString()
    .isLength({ min: 1, max: 100 })
    .trim()
    .withMessage('Name must be between 1 and 100 characters'),
  
  // Optional name validation
  optionalName: body('name')
    .optional()
    .isString()
    .isLength({ min: 1, max: 100 })
    .trim()
    .withMessage('Name must be between 1 and 100 characters'),
  
  // Title validation
  title: body('title')
    .isString()
    .isLength({ min: 1, max: 200 })
    .trim()
    .withMessage('Title must be between 1 and 200 characters'),
  
  // Content validation
  content: body('content')
    .optional()
    .isString()
    .isLength({ max: 50000 })
    .withMessage('Content must not exceed 50,000 characters'),
  
  // Category validation
  category: body('category')
    .optional()
    .isIn(['MEETING', 'LECTURE', 'INTERVIEW', 'PERSONAL', 'OTHER'])
    .withMessage('Invalid category'),
  
  // Tags validation
  tags: body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array')
    .custom((tags: string[]) => {
      if (tags.length > 20) {
        throw new Error('Maximum 20 tags allowed');
      }
      for (const tag of tags) {
        if (typeof tag !== 'string' || tag.length > 50) {
          throw new Error('Each tag must be a string with maximum 50 characters');
        }
      }
      return true;
    }),
  
  // Pagination validation
  page: query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  limit: query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  // Search query validation
  searchQuery: query('q')
    .optional()
    .isString()
    .isLength({ min: 1, max: 200 })
    .trim()
    .withMessage('Search query must be between 1 and 200 characters'),
  
  // Date range validation
  dateFrom: query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Invalid date format for dateFrom'),
  
  dateTo: query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Invalid date format for dateTo'),
  
  // Audio file validation
  audioFile: body('audioFile')
    .custom((value, { req }) => {
      if (!req.file) {
        throw new Error('Audio file is required');
      }
      
      const allowedMimeTypes = [
        'audio/mpeg',
        'audio/wav',
        'audio/mp4',
        'audio/webm',
        'audio/ogg'
      ];
      
      if (!allowedMimeTypes.includes(req.file.mimetype)) {
        throw new Error('Invalid audio file format');
      }
      
      // Max file size: 100MB
      const maxSize = 100 * 1024 * 1024;
      if (req.file.size > maxSize) {
        throw new Error('Audio file too large (max 100MB)');
      }
      
      return true;
    }),
  
  // Language validation
  language: body('language')
    .optional()
    .isIn(['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'])
    .withMessage('Unsupported language'),
  
  // Boolean validation
  boolean: (field: string) => body(field)
    .optional()
    .isBoolean()
    .withMessage(`${field} must be a boolean`),
  
  // URL validation
  url: (field: string) => body(field)
    .optional()
    .isURL()
    .withMessage(`${field} must be a valid URL`)
};

// Authentication validations
export const authValidations = {
  register: [
    commonValidations.email,
    commonValidations.password,
    commonValidations.name,
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.password) {
          throw new Error('Passwords do not match');
        }
        return true;
      }),
    handleValidationErrors
  ],
  
  login: [
    commonValidations.email,
    body('password').notEmpty().withMessage('Password is required'),
    body('rememberMe').optional().isBoolean().withMessage('Remember me must be a boolean'),
    handleValidationErrors
  ],
  
  refreshToken: [
    body('refreshToken').notEmpty().withMessage('Refresh token is required'),
    handleValidationErrors
  ],
  
  forgotPassword: [
    commonValidations.email,
    handleValidationErrors
  ],
  
  resetPassword: [
    body('token').notEmpty().withMessage('Reset token is required'),
    commonValidations.password,
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.password) {
          throw new Error('Passwords do not match');
        }
        return true;
      }),
    handleValidationErrors
  ],
  
  changePassword: [
    body('currentPassword').notEmpty().withMessage('Current password is required'),
    commonValidations.password.withMessage('New password must meet requirements'),
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.password) {
          throw new Error('Passwords do not match');
        }
        return true;
      }),
    handleValidationErrors
  ]
};

// Note validations
export const noteValidations = {
  create: [
    commonValidations.title,
    commonValidations.content,
    commonValidations.category,
    commonValidations.tags,
    handleValidationErrors
  ],
  
  update: [
    commonValidations.id,
    body('title').optional().isString().isLength({ min: 1, max: 200 }).trim(),
    commonValidations.content,
    commonValidations.category,
    commonValidations.tags,
    commonValidations.boolean('isPublic'),
    commonValidations.boolean('isFavorite'),
    handleValidationErrors
  ],
  
  delete: [
    commonValidations.id,
    handleValidationErrors
  ],
  
  get: [
    commonValidations.id,
    handleValidationErrors
  ],
  
  list: [
    commonValidations.page,
    commonValidations.limit,
    commonValidations.searchQuery,
    commonValidations.category.optional(),
    commonValidations.dateFrom,
    commonValidations.dateTo,
    query('sortBy')
      .optional()
      .isIn(['createdAt', 'updatedAt', 'title'])
      .withMessage('Invalid sort field'),
    query('sortOrder')
      .optional()
      .isIn(['asc', 'desc'])
      .withMessage('Invalid sort order'),
    handleValidationErrors
  ]
};

// Audio validations
export const audioValidations = {
  upload: [
    // File validation is handled by multer middleware
    body('title').optional().isString().isLength({ min: 1, max: 200 }).trim(),
    commonValidations.language,
    body('speakerIdentification').optional().isBoolean(),
    handleValidationErrors
  ],
  
  transcribe: [
    commonValidations.id,
    commonValidations.language,
    body('speakerIdentification').optional().isBoolean(),
    body('timestamps').optional().isBoolean(),
    handleValidationErrors
  ],
  
  delete: [
    commonValidations.id,
    handleValidationErrors
  ]
};

// Search validations
export const searchValidations = {
  search: [
    commonValidations.searchQuery.notEmpty().withMessage('Search query is required'),
    commonValidations.page,
    commonValidations.limit,
    query('type')
      .optional()
      .isIn(['notes', 'transcriptions', 'all'])
      .withMessage('Invalid search type'),
    query('filters')
      .optional()
      .isJSON()
      .withMessage('Filters must be valid JSON'),
    handleValidationErrors
  ]
};

// User validations
export const userValidations = {
  updateProfile: [
    commonValidations.optionalName,
    body('avatar').optional().isURL().withMessage('Avatar must be a valid URL'),
    body('preferences').optional().isJSON().withMessage('Preferences must be valid JSON'),
    handleValidationErrors
  ],
  
  updateSettings: [
    body('language').optional().isIn(['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh']),
    body('timezone').optional().isString(),
    body('notifications').optional().isJSON(),
    handleValidationErrors
  ]
};

// Custom validation middleware factory
export const createValidation = (validations: ValidationChain[]) => {
  return [...validations, handleValidationErrors];
};

export default {
  handleValidationErrors,
  commonValidations,
  authValidations,
  noteValidations,
  audioValidations,
  searchValidations,
  userValidations,
  createValidation
};
