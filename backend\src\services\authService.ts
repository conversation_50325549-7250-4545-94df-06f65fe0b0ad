import jwt from 'jsonwebtoken';
import { Redis } from 'ioredis';
import { redisClient } from '../config/redis';
import { logger } from '../utils/logger';

interface TokenPayload {
  userId: string;
  type: 'access' | 'refresh';
  iat?: number;
  exp?: number;
}

interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

export class AuthService {
  private redis: Redis;
  private jwtSecret: string;
  private jwtRefreshSecret: string;
  private accessTokenExpiry: string;
  private refreshTokenExpiry: string;

  constructor() {
    this.redis = redisClient;
    this.jwtSecret = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
    this.jwtRefreshSecret = process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key';
    this.accessTokenExpiry = process.env.JWT_EXPIRY || '15m';
    this.refreshTokenExpiry = process.env.JWT_REFRESH_EXPIRY || '7d';

    if (!process.env.JWT_SECRET || !process.env.JWT_REFRESH_SECRET) {
      logger.warn('JWT secrets not set in environment variables. Using default values.');
    }
  }

  /**
   * Generate access and refresh token pair
   */
  public async generateTokens(userId: string, customExpiry?: string): Promise<TokenPair> {
    try {
      const accessTokenExpiry = customExpiry || this.accessTokenExpiry;
      
      // Generate access token
      const accessTokenPayload: TokenPayload = {
        userId,
        type: 'access'
      };

      const accessToken = jwt.sign(accessTokenPayload, this.jwtSecret, {
        expiresIn: accessTokenExpiry,
        issuer: 'quickcapt-api',
        audience: 'quickcapt-client'
      });

      // Generate refresh token
      const refreshTokenPayload: TokenPayload = {
        userId,
        type: 'refresh'
      };

      const refreshToken = jwt.sign(refreshTokenPayload, this.jwtRefreshSecret, {
        expiresIn: this.refreshTokenExpiry,
        issuer: 'quickcapt-api',
        audience: 'quickcapt-client'
      });

      // Store refresh token in Redis with expiration
      const refreshTokenKey = `refresh_token:${userId}:${this.generateTokenId()}`;
      const refreshTokenExpSeconds = this.getExpirationSeconds(this.refreshTokenExpiry);
      
      await this.redis.setex(refreshTokenKey, refreshTokenExpSeconds, refreshToken);

      // Store token metadata
      await this.redis.setex(
        `token_meta:${userId}`,
        refreshTokenExpSeconds,
        JSON.stringify({
          lastGenerated: new Date().toISOString(),
          refreshTokenKey
        })
      );

      logger.info(`Tokens generated for user: ${userId}`);

      return {
        accessToken,
        refreshToken
      };

    } catch (error) {
      logger.error('Error generating tokens:', error);
      throw new Error('Failed to generate authentication tokens');
    }
  }

  /**
   * Verify access token
   */
  public async verifyAccessToken(token: string): Promise<TokenPayload | null> {
    try {
      // Check if token is blacklisted
      const isBlacklisted = await this.isTokenBlacklisted(token);
      if (isBlacklisted) {
        logger.warn('Attempted to use blacklisted token');
        return null;
      }

      const decoded = jwt.verify(token, this.jwtSecret, {
        issuer: 'quickcapt-api',
        audience: 'quickcapt-client'
      }) as TokenPayload;

      if (decoded.type !== 'access') {
        logger.warn('Invalid token type for access token verification');
        return null;
      }

      return decoded;

    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        logger.warn('Invalid access token:', error.message);
      } else if (error instanceof jwt.TokenExpiredError) {
        logger.info('Access token expired');
      } else {
        logger.error('Error verifying access token:', error);
      }
      return null;
    }
  }

  /**
   * Verify refresh token
   */
  public async verifyRefreshToken(token: string): Promise<TokenPayload | null> {
    try {
      const decoded = jwt.verify(token, this.jwtRefreshSecret, {
        issuer: 'quickcapt-api',
        audience: 'quickcapt-client'
      }) as TokenPayload;

      if (decoded.type !== 'refresh') {
        logger.warn('Invalid token type for refresh token verification');
        return null;
      }

      // Check if refresh token exists in Redis
      const pattern = `refresh_token:${decoded.userId}:*`;
      const keys = await this.redis.keys(pattern);
      
      let tokenExists = false;
      for (const key of keys) {
        const storedToken = await this.redis.get(key);
        if (storedToken === token) {
          tokenExists = true;
          break;
        }
      }

      if (!tokenExists) {
        logger.warn('Refresh token not found in store');
        return null;
      }

      return decoded;

    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        logger.warn('Invalid refresh token:', error.message);
      } else if (error instanceof jwt.TokenExpiredError) {
        logger.info('Refresh token expired');
      } else {
        logger.error('Error verifying refresh token:', error);
      }
      return null;
    }
  }

  /**
   * Invalidate token (add to blacklist)
   */
  public async invalidateToken(token: string): Promise<void> {
    try {
      const decoded = jwt.decode(token) as TokenPayload;
      if (!decoded || !decoded.exp) {
        return;
      }

      // Calculate remaining TTL
      const now = Math.floor(Date.now() / 1000);
      const ttl = decoded.exp - now;

      if (ttl > 0) {
        // Add to blacklist with TTL
        await this.redis.setex(`blacklist:${token}`, ttl, '1');
        logger.info(`Token blacklisted for user: ${decoded.userId}`);
      }

    } catch (error) {
      logger.error('Error invalidating token:', error);
    }
  }

  /**
   * Invalidate all tokens for a user
   */
  public async invalidateAllUserTokens(userId: string): Promise<void> {
    try {
      // Remove all refresh tokens for the user
      const pattern = `refresh_token:${userId}:*`;
      const keys = await this.redis.keys(pattern);
      
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }

      // Remove token metadata
      await this.redis.del(`token_meta:${userId}`);

      logger.info(`All tokens invalidated for user: ${userId}`);

    } catch (error) {
      logger.error('Error invalidating all user tokens:', error);
    }
  }

  /**
   * Check if token is blacklisted
   */
  private async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const result = await this.redis.get(`blacklist:${token}`);
      return result !== null;
    } catch (error) {
      logger.error('Error checking token blacklist:', error);
      return false;
    }
  }

  /**
   * Generate unique token ID
   */
  private generateTokenId(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * Convert expiry string to seconds
   */
  private getExpirationSeconds(expiry: string): number {
    const unit = expiry.slice(-1);
    const value = parseInt(expiry.slice(0, -1));

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 24 * 60 * 60;
      default: return 3600; // 1 hour default
    }
  }

  /**
   * Get user's active sessions
   */
  public async getUserSessions(userId: string): Promise<Array<{
    tokenId: string;
    createdAt: string;
    expiresAt: string;
  }>> {
    try {
      const pattern = `refresh_token:${userId}:*`;
      const keys = await this.redis.keys(pattern);
      
      const sessions = [];
      for (const key of keys) {
        const token = await this.redis.get(key);
        const ttl = await this.redis.ttl(key);
        
        if (token && ttl > 0) {
          const tokenId = key.split(':')[2];
          const expiresAt = new Date(Date.now() + (ttl * 1000)).toISOString();
          
          sessions.push({
            tokenId,
            createdAt: new Date(Date.now() - (this.getExpirationSeconds(this.refreshTokenExpiry) - ttl) * 1000).toISOString(),
            expiresAt
          });
        }
      }

      return sessions;

    } catch (error) {
      logger.error('Error getting user sessions:', error);
      return [];
    }
  }

  /**
   * Revoke specific session
   */
  public async revokeSession(userId: string, tokenId: string): Promise<boolean> {
    try {
      const key = `refresh_token:${userId}:${tokenId}`;
      const result = await this.redis.del(key);
      
      if (result > 0) {
        logger.info(`Session revoked for user ${userId}, token ${tokenId}`);
        return true;
      }
      
      return false;

    } catch (error) {
      logger.error('Error revoking session:', error);
      return false;
    }
  }

  /**
   * Clean up expired tokens
   */
  public async cleanupExpiredTokens(): Promise<void> {
    try {
      // Redis automatically handles TTL, but we can clean up any orphaned keys
      const blacklistPattern = 'blacklist:*';
      const blacklistKeys = await this.redis.keys(blacklistPattern);
      
      for (const key of blacklistKeys) {
        const ttl = await this.redis.ttl(key);
        if (ttl <= 0) {
          await this.redis.del(key);
        }
      }

      logger.info('Token cleanup completed');

    } catch (error) {
      logger.error('Error during token cleanup:', error);
    }
  }

  /**
   * Get token statistics
   */
  public async getTokenStats(): Promise<{
    activeRefreshTokens: number;
    blacklistedTokens: number;
  }> {
    try {
      const refreshTokenKeys = await this.redis.keys('refresh_token:*');
      const blacklistKeys = await this.redis.keys('blacklist:*');

      return {
        activeRefreshTokens: refreshTokenKeys.length,
        blacklistedTokens: blacklistKeys.length
      };

    } catch (error) {
      logger.error('Error getting token stats:', error);
      return {
        activeRefreshTokens: 0,
        blacklistedTokens: 0
      };
    }
  }
}

export default AuthService;
