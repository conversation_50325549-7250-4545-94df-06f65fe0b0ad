import { useState, useCallback } from 'react';
import { api } from '../services/api';
import { SearchResult, SearchFilters, SearchFacets } from '../types';
import toast from 'react-hot-toast';

interface UseSearchReturn {
  results: SearchResult[];
  loading: boolean;
  error: string | null;
  suggestions: string[];
  facets: SearchFacets | null;
  search: (query: string, type?: 'all' | 'notes' | 'transcriptions', filters?: SearchFilters) => Promise<void>;
  getSuggestions: (query: string) => Promise<void>;
  getFacets: () => Promise<void>;
  clearResults: () => void;
}

export const useSearch = (): UseSearchReturn => {
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [facets, setFacets] = useState<SearchFacets | null>(null);

  const search = useCallback(async (
    query: string,
    type: 'all' | 'notes' | 'transcriptions' = 'all',
    filters: SearchFilters = {}
  ) => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        q: query.trim(),
        type,
        page: '1',
        limit: '20'
      });

      // Add filters to params
      if (filters.category) {
        params.append('category', filters.category);
      }
      if (filters.dateFrom) {
        params.append('dateFrom', filters.dateFrom);
      }
      if (filters.dateTo) {
        params.append('dateTo', filters.dateTo);
      }
      if (filters.tags && filters.tags.length > 0) {
        params.append('tags', filters.tags.join(','));
      }

      const response = await api.get(`/search?${params.toString()}`);
      
      if (response.data.success) {
        setResults(response.data.data.results || []);
      } else {
        throw new Error(response.data.error?.message || 'Search failed');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error?.message || err.message || 'Search failed';
      setError(errorMessage);
      setResults([]);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const getSuggestions = useCallback(async (query: string) => {
    if (!query.trim() || query.length < 2) {
      setSuggestions([]);
      return;
    }

    try {
      const response = await api.get(`/search/suggestions?q=${encodeURIComponent(query.trim())}&limit=5`);
      
      if (response.data.success) {
        setSuggestions(response.data.data.suggestions || []);
      }
    } catch (err: any) {
      // Silently fail for suggestions
      console.warn('Failed to get search suggestions:', err);
      setSuggestions([]);
    }
  }, []);

  const getFacets = useCallback(async () => {
    try {
      const response = await api.get('/search/facets');
      
      if (response.data.success) {
        setFacets(response.data.data);
      }
    } catch (err: any) {
      console.warn('Failed to get search facets:', err);
      setFacets(null);
    }
  }, []);

  const clearResults = useCallback(() => {
    setResults([]);
    setError(null);
    setSuggestions([]);
  }, []);

  return {
    results,
    loading,
    error,
    suggestions,
    facets,
    search,
    getSuggestions,
    getFacets,
    clearResults
  };
};
