# QuickCapt Environment Configuration

# Application
NODE_ENV=development
PORT=3001
FRONTEND_PORT=3000
AI_SERVICES_PORT=3002

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/quickcapt
DB_HOST=localhost
DB_PORT=5432
DB_NAME=quickcapt
DB_USER=username
DB_PASSWORD=password

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your-refresh-token-secret-change-this
REFRESH_TOKEN_EXPIRES_IN=30d

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# Audio Processing
MAX_AUDIO_FILE_SIZE=100MB
SUPPORTED_AUDIO_FORMATS=mp3,wav,m4a,webm,ogg
AUDIO_SAMPLE_RATE=44100
AUDIO_CHANNELS=1

# File Storage
STORAGE_TYPE=local
STORAGE_PATH=./uploads
MAX_STORAGE_SIZE=10GB

# AWS S3 (if using cloud storage)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=quickcapt-audio-files

# Elasticsearch (for search functionality)
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX=quickcapt_notes

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,https://quickcapt.com

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key-here
ENCRYPTION_ALGORITHM=aes-256-gcm

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30000
WS_MAX_CONNECTIONS=1000

# AI Services Configuration
WHISPER_MODEL=base
SPEAKER_DIARIZATION_ENABLED=true
LANGUAGE_DETECTION_ENABLED=true
SENTIMENT_ANALYSIS_ENABLED=true

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>

# Monitoring & Analytics
SENTRY_DSN=your-sentry-dsn-here
GOOGLE_ANALYTICS_ID=your-ga-id-here

# Feature Flags
ENABLE_REAL_TIME_TRANSCRIPTION=true
ENABLE_SPEAKER_IDENTIFICATION=true
ENABLE_OFFLINE_MODE=true
ENABLE_COLLABORATION=true
ENABLE_EXPORT_FEATURES=true

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this
CSRF_SECRET=your-csrf-secret-change-this

# Development
DEBUG=quickcapt:*
MOCK_AI_RESPONSES=false
ENABLE_API_DOCS=true
