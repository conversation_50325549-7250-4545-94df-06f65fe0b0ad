# Netlify configuration for QuickCapt frontend deployment

[build]
  # Build command
  command = "cd frontend && npm run build"
  
  # Directory to publish
  publish = "frontend/build"
  
  # Environment variables for build
  environment = { NODE_ENV = "production", GENERATE_SOURCEMAP = "false" }

[build.processing]
  # Skip processing of images, CSS, and JS for faster builds
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

[build.processing.images]
  compress = true

# Redirect rules for SPA
[[redirects]]
  from = "/api/*"
  to = "https://quickcapt-api.herokuapp.com/api/:splat"
  status = 200
  force = true
  headers = {X-From = "Netlify"}

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(self), geolocation=(), payment=()"
    
    # Performance headers
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/index.html"
  [headers.values]
    # Don't cache the main HTML file
    Cache-Control = "public, max-age=0, must-revalidate"
    
    # Security headers for main page
    Content-Security-Policy = """
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com https://www.googletagmanager.com;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
      font-src 'self' https://fonts.gstatic.com;
      img-src 'self' data: https: blob:;
      media-src 'self' blob:;
      connect-src 'self' https://api.quickcapt.com wss://api.quickcapt.com https://www.google-analytics.com;
      worker-src 'self' blob:;
      frame-src 'none';
      object-src 'none';
      base-uri 'self';
      form-action 'self';
    """

[[headers]]
  for = "/static/js/*"
  [headers.values]
    # Cache JavaScript files for 1 year
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/static/css/*"
  [headers.values]
    # Cache CSS files for 1 year
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/static/media/*"
  [headers.values]
    # Cache media files for 1 year
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/manifest.json"
  [headers.values]
    # Cache manifest for 1 week
    Cache-Control = "public, max-age=604800"

[[headers]]
  for = "/sw.js"
  [headers.values]
    # Don't cache service worker
    Cache-Control = "public, max-age=0, must-revalidate"

# Environment-specific settings
[context.production]
  command = "cd frontend && npm run build"
  environment = { 
    NODE_ENV = "production",
    REACT_APP_API_URL = "https://api.quickcapt.com",
    REACT_APP_WS_URL = "wss://api.quickcapt.com",
    REACT_APP_ENVIRONMENT = "production",
    GENERATE_SOURCEMAP = "false"
  }

[context.staging]
  command = "cd frontend && npm run build"
  environment = { 
    NODE_ENV = "production",
    REACT_APP_API_URL = "https://staging-api.quickcapt.com",
    REACT_APP_WS_URL = "wss://staging-api.quickcapt.com",
    REACT_APP_ENVIRONMENT = "staging",
    GENERATE_SOURCEMAP = "true"
  }

[context.deploy-preview]
  command = "cd frontend && npm run build"
  environment = { 
    NODE_ENV = "production",
    REACT_APP_API_URL = "https://preview-api.quickcapt.com",
    REACT_APP_WS_URL = "wss://preview-api.quickcapt.com",
    REACT_APP_ENVIRONMENT = "preview",
    GENERATE_SOURCEMAP = "true"
  }

[context.branch-deploy]
  command = "cd frontend && npm run build"
  environment = { 
    NODE_ENV = "production",
    REACT_APP_API_URL = "https://dev-api.quickcapt.com",
    REACT_APP_WS_URL = "wss://dev-api.quickcapt.com",
    REACT_APP_ENVIRONMENT = "development",
    GENERATE_SOURCEMAP = "true"
  }

# Functions configuration (if using Netlify Functions)
[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# Forms configuration
[forms]
  # Enable form processing
  enabled = true

# Large Media configuration
[large_media]
  # Enable Git LFS for large files
  enabled = false

# Split testing configuration
[split_testing]
  # Enable split testing
  enabled = false

# Analytics configuration
[analytics]
  # Enable analytics
  enabled = true

# Edge handlers configuration
[edge_handlers]
  # Enable edge handlers
  enabled = false

# Plugin configuration
[[plugins]]
  package = "@netlify/plugin-lighthouse"
  
  [plugins.inputs.thresholds]
    performance = 0.9
    accessibility = 0.9
    best-practices = 0.9
    seo = 0.9

[[plugins]]
  package = "netlify-plugin-submit-sitemap"
  
  [plugins.inputs]
    baseUrl = "https://quickcapt.com"
    sitemapPath = "/sitemap.xml"
    ignorePeriod = 0
    providers = [
      "google",
      "bing",
      "yandex"
    ]

[[plugins]]
  package = "@netlify/plugin-sitemap"
  
  [plugins.inputs]
    buildDir = "frontend/build"
    exclude = [
      "/admin/**",
      "/private/**"
    ]

# Build hooks
[build.hooks]
  # Webhook for triggering builds
  # incoming-hooks = ["https://api.netlify.com/build_hooks/YOUR_BUILD_HOOK_ID"]

# Dev server configuration
[dev]
  command = "cd frontend && npm start"
  port = 3000
  publish = "frontend/build"
  autoLaunch = false
  
  # Environment variables for development
  [dev.env]
    REACT_APP_API_URL = "http://localhost:3001"
    REACT_APP_WS_URL = "ws://localhost:3001"
    REACT_APP_ENVIRONMENT = "development"
