# Google Cloud Build configuration for QuickCapt

steps:
  # Install dependencies for backend
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['ci', '--only=production']
    dir: 'backend'
    id: 'install-backend-deps'

  # Install dependencies for frontend
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['ci']
    dir: 'frontend'
    id: 'install-frontend-deps'

  # Install dependencies for AI services
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['ci', '--only=production']
    dir: 'ai-services'
    id: 'install-ai-deps'

  # Run backend tests
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['test']
    dir: 'backend'
    id: 'test-backend'
    waitFor: ['install-backend-deps']
    env:
      - 'NODE_ENV=test'
      - 'DATABASE_URL=postgresql://test:test@localhost:5432/test'

  # Run frontend tests
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['test', '--', '--coverage', '--watchAll=false']
    dir: 'frontend'
    id: 'test-frontend'
    waitFor: ['install-frontend-deps']
    env:
      - 'CI=true'

  # Build frontend
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['run', 'build']
    dir: 'frontend'
    id: 'build-frontend'
    waitFor: ['test-frontend']
    env:
      - 'NODE_ENV=production'
      - 'REACT_APP_API_URL=https://api.quickcapt.com'
      - 'REACT_APP_WS_URL=wss://api.quickcapt.com'
      - 'GENERATE_SOURCEMAP=false'

  # Build backend
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['run', 'build']
    dir: 'backend'
    id: 'build-backend'
    waitFor: ['test-backend']

  # Build AI services
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['run', 'build']
    dir: 'ai-services'
    id: 'build-ai-services'
    waitFor: ['install-ai-deps']

  # Build Docker image for backend
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/quickcapt-backend:$BUILD_ID',
      '-t', 'gcr.io/$PROJECT_ID/quickcapt-backend:latest',
      '-f', 'docker/Dockerfile.backend',
      '.'
    ]
    id: 'build-backend-image'
    waitFor: ['build-backend']

  # Build Docker image for AI services
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/quickcapt-ai-services:$BUILD_ID',
      '-t', 'gcr.io/$PROJECT_ID/quickcapt-ai-services:latest',
      '-f', 'docker/Dockerfile.ai-services',
      '.'
    ]
    id: 'build-ai-image'
    waitFor: ['build-ai-services']

  # Push backend image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/quickcapt-backend:$BUILD_ID']
    id: 'push-backend-image'
    waitFor: ['build-backend-image']

  # Push AI services image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/quickcapt-ai-services:$BUILD_ID']
    id: 'push-ai-image'
    waitFor: ['build-ai-image']

  # Deploy to Cloud Run - Backend
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', 'quickcapt-backend',
      '--image', 'gcr.io/$PROJECT_ID/quickcapt-backend:$BUILD_ID',
      '--region', '${_REGION}',
      '--platform', 'managed',
      '--allow-unauthenticated',
      '--memory', '2Gi',
      '--cpu', '2',
      '--concurrency', '80',
      '--max-instances', '10',
      '--min-instances', '1',
      '--port', '3001',
      '--set-env-vars', 'NODE_ENV=production,PORT=3001',
      '--set-secrets', 'DATABASE_URL=database-url:latest,JWT_SECRET=jwt-secret:latest,OPENAI_API_KEY=openai-key:latest'
    ]
    id: 'deploy-backend'
    waitFor: ['push-backend-image']

  # Deploy to Cloud Run - AI Services
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', 'quickcapt-ai-services',
      '--image', 'gcr.io/$PROJECT_ID/quickcapt-ai-services:$BUILD_ID',
      '--region', '${_REGION}',
      '--platform', 'managed',
      '--allow-unauthenticated',
      '--memory', '4Gi',
      '--cpu', '2',
      '--concurrency', '10',
      '--max-instances', '5',
      '--min-instances', '0',
      '--port', '3002',
      '--set-env-vars', 'NODE_ENV=production,PORT=3002',
      '--set-secrets', 'OPENAI_API_KEY=openai-key:latest'
    ]
    id: 'deploy-ai-services'
    waitFor: ['push-ai-image']

  # Deploy frontend to Firebase Hosting
  - name: 'gcr.io/$PROJECT_ID/firebase'
    args: ['deploy', '--only=hosting', '--project=$PROJECT_ID']
    dir: 'frontend'
    id: 'deploy-frontend'
    waitFor: ['build-frontend']

  # Run database migrations
  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        gcloud run jobs create quickcapt-migrate \
          --image gcr.io/$PROJECT_ID/quickcapt-backend:$BUILD_ID \
          --region ${_REGION} \
          --set-secrets DATABASE_URL=database-url:latest \
          --set-env-vars NODE_ENV=production \
          --command npm \
          --args "run,db:migrate" \
          --max-retries 3 \
          --parallelism 1 \
          --task-count 1 \
          --task-timeout 600 \
          --replace || true
        
        gcloud run jobs execute quickcapt-migrate --region ${_REGION} --wait
    id: 'run-migrations'
    waitFor: ['deploy-backend']

  # Update Cloud Endpoints configuration
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'endpoints', 'services', 'deploy',
      'gcp/openapi.yaml',
      '--project', '$PROJECT_ID'
    ]
    id: 'deploy-endpoints'
    waitFor: ['deploy-backend', 'deploy-ai-services']

# Substitutions for build variables
substitutions:
  _REGION: us-central1
  _ENVIRONMENT: production

# Build options
options:
  # Use high-performance machine type
  machineType: 'E2_HIGHCPU_8'
  
  # Disk size
  diskSizeGb: 100
  
  # Logging
  logging: CLOUD_LOGGING_ONLY
  
  # Source provenance
  sourceProvenanceHash: ['SHA256']

# Build timeout
timeout: '1800s'

# Images to be pushed to Container Registry
images:
  - 'gcr.io/$PROJECT_ID/quickcapt-backend:$BUILD_ID'
  - 'gcr.io/$PROJECT_ID/quickcapt-backend:latest'
  - 'gcr.io/$PROJECT_ID/quickcapt-ai-services:$BUILD_ID'
  - 'gcr.io/$PROJECT_ID/quickcapt-ai-services:latest'

# Artifacts to store
artifacts:
  objects:
    location: 'gs://$PROJECT_ID-build-artifacts'
    paths:
      - 'frontend/build/**/*'
      - 'backend/dist/**/*'
      - 'ai-services/dist/**/*'

# Tags for organization
tags:
  - 'quickcapt'
  - 'production'
  - 'automated'
