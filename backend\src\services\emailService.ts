import nodemailer from 'nodemailer';
import { logger } from '../utils/logger';

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

interface EmailOptions {
  to: string;
  subject: string;
  html?: string;
  text?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

export class EmailService {
  private transporter: nodemailer.Transporter;
  private fromEmail: string;
  private fromName: string;

  constructor() {
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    this.fromName = process.env.FROM_NAME || 'QuickCapt';

    // Configure transporter based on environment
    if (process.env.NODE_ENV === 'production') {
      // Production: Use SendGrid, AWS SES, or similar
      this.transporter = nodemailer.createTransporter({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      });
    } else {
      // Development: Use Ethereal Email for testing
      this.transporter = nodemailer.createTransporter({
        host: 'smtp.ethereal.email',
        port: 587,
        auth: {
          user: '<EMAIL>',
          pass: 'ethereal.pass'
        }
      });
    }
  }

  /**
   * Send email
   */
  async sendEmail(options: EmailOptions): Promise<void> {
    try {
      const mailOptions = {
        from: `${this.fromName} <${this.fromEmail}>`,
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text,
        attachments: options.attachments
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      logger.info(`Email sent successfully to ${options.to}`, {
        messageId: result.messageId,
        subject: options.subject
      });

      // Log preview URL in development
      if (process.env.NODE_ENV !== 'production') {
        logger.info(`Preview URL: ${nodemailer.getTestMessageUrl(result)}`);
      }
    } catch (error) {
      logger.error('Failed to send email:', error);
      throw new Error('Failed to send email');
    }
  }

  /**
   * Send welcome email
   */
  async sendWelcomeEmail(to: string, name: string): Promise<void> {
    const template = this.getWelcomeTemplate(name);
    await this.sendEmail({
      to,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * Send email verification
   */
  async sendEmailVerification(to: string, name: string, verificationUrl: string): Promise<void> {
    const template = this.getEmailVerificationTemplate(name, verificationUrl);
    await this.sendEmail({
      to,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * Send password reset email
   */
  async sendPasswordReset(to: string, name: string, resetUrl: string): Promise<void> {
    const template = this.getPasswordResetTemplate(name, resetUrl);
    await this.sendEmail({
      to,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * Send transcription complete notification
   */
  async sendTranscriptionComplete(to: string, name: string, noteTitle: string, noteUrl: string): Promise<void> {
    const template = this.getTranscriptionCompleteTemplate(name, noteTitle, noteUrl);
    await this.sendEmail({
      to,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * Send subscription confirmation
   */
  async sendSubscriptionConfirmation(to: string, name: string, planName: string): Promise<void> {
    const template = this.getSubscriptionConfirmationTemplate(name, planName);
    await this.sendEmail({
      to,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * Send payment failed notification
   */
  async sendPaymentFailed(to: string, name: string, updateUrl: string): Promise<void> {
    const template = this.getPaymentFailedTemplate(name, updateUrl);
    await this.sendEmail({
      to,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * Welcome email template
   */
  private getWelcomeTemplate(name: string): EmailTemplate {
    return {
      subject: 'Welcome to QuickCapt! 🎉',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #3b82f6;">Welcome to QuickCapt, ${name}!</h1>
          <p>Thank you for joining QuickCapt, the AI-powered note taking revolution.</p>
          
          <h2>Get Started:</h2>
          <ul>
            <li>📝 Create your first note</li>
            <li>🎤 Record and transcribe audio</li>
            <li>🤖 Get AI-powered insights</li>
            <li>🔍 Search through your notes</li>
          </ul>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="https://quickcapt.com/dashboard" 
               style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
              Get Started
            </a>
          </div>
          
          <p>If you have any questions, feel free to reach out to our support team.</p>
          
          <p>Best regards,<br>The QuickCapt Team</p>
        </div>
      `,
      text: `Welcome to QuickCapt, ${name}!\n\nThank you for joining QuickCapt, the AI-powered note taking revolution.\n\nGet started by visiting: https://quickcapt.com/dashboard\n\nBest regards,\nThe QuickCapt Team`
    };
  }

  /**
   * Email verification template
   */
  private getEmailVerificationTemplate(name: string, verificationUrl: string): EmailTemplate {
    return {
      subject: 'Verify your QuickCapt email address',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #3b82f6;">Verify Your Email Address</h1>
          <p>Hi ${name},</p>
          <p>Please click the button below to verify your email address and complete your QuickCapt registration.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" 
               style="background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
              Verify Email Address
            </a>
          </div>
          
          <p>If you didn't create a QuickCapt account, you can safely ignore this email.</p>
          
          <p>This link will expire in 24 hours.</p>
          
          <p>Best regards,<br>The QuickCapt Team</p>
        </div>
      `,
      text: `Hi ${name},\n\nPlease verify your email address by clicking this link: ${verificationUrl}\n\nThis link will expire in 24 hours.\n\nBest regards,\nThe QuickCapt Team`
    };
  }

  /**
   * Password reset template
   */
  private getPasswordResetTemplate(name: string, resetUrl: string): EmailTemplate {
    return {
      subject: 'Reset your QuickCapt password',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #3b82f6;">Reset Your Password</h1>
          <p>Hi ${name},</p>
          <p>We received a request to reset your QuickCapt password. Click the button below to create a new password.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background: #ef4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
              Reset Password
            </a>
          </div>
          
          <p>If you didn't request a password reset, you can safely ignore this email.</p>
          
          <p>This link will expire in 1 hour.</p>
          
          <p>Best regards,<br>The QuickCapt Team</p>
        </div>
      `,
      text: `Hi ${name},\n\nReset your password by clicking this link: ${resetUrl}\n\nThis link will expire in 1 hour.\n\nBest regards,\nThe QuickCapt Team`
    };
  }

  /**
   * Transcription complete template
   */
  private getTranscriptionCompleteTemplate(name: string, noteTitle: string, noteUrl: string): EmailTemplate {
    return {
      subject: `Your transcription is ready: ${noteTitle}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #3b82f6;">Transcription Complete! 🎉</h1>
          <p>Hi ${name},</p>
          <p>Your audio transcription for "<strong>${noteTitle}</strong>" is now ready.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${noteUrl}" 
               style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
              View Transcription
            </a>
          </div>
          
          <p>You can now:</p>
          <ul>
            <li>📝 Edit and refine the transcription</li>
            <li>🤖 Get AI-powered insights and summaries</li>
            <li>🔍 Search through the content</li>
            <li>📤 Share with collaborators</li>
          </ul>
          
          <p>Best regards,<br>The QuickCapt Team</p>
        </div>
      `,
      text: `Hi ${name},\n\nYour transcription for "${noteTitle}" is ready!\n\nView it here: ${noteUrl}\n\nBest regards,\nThe QuickCapt Team`
    };
  }

  /**
   * Subscription confirmation template
   */
  private getSubscriptionConfirmationTemplate(name: string, planName: string): EmailTemplate {
    return {
      subject: `Welcome to QuickCapt ${planName}! 🚀`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #3b82f6;">Welcome to ${planName}!</h1>
          <p>Hi ${name},</p>
          <p>Thank you for upgrading to QuickCapt ${planName}. Your subscription is now active!</p>
          
          <h2>Your new features include:</h2>
          <ul>
            <li>🎤 Unlimited audio recording</li>
            <li>🤖 Advanced AI analysis</li>
            <li>👥 Speaker identification</li>
            <li>📊 Export options</li>
            <li>⚡ Priority support</li>
          </ul>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="https://quickcapt.com/dashboard" 
               style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
              Start Using Your New Features
            </a>
          </div>
          
          <p>Manage your subscription anytime in your account settings.</p>
          
          <p>Best regards,<br>The QuickCapt Team</p>
        </div>
      `,
      text: `Hi ${name},\n\nWelcome to QuickCapt ${planName}! Your subscription is now active.\n\nStart using your new features: https://quickcapt.com/dashboard\n\nBest regards,\nThe QuickCapt Team`
    };
  }

  /**
   * Payment failed template
   */
  private getPaymentFailedTemplate(name: string, updateUrl: string): EmailTemplate {
    return {
      subject: 'Action Required: Update your payment method',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #ef4444;">Payment Failed</h1>
          <p>Hi ${name},</p>
          <p>We were unable to process your recent payment for your QuickCapt subscription.</p>
          
          <p>To avoid any interruption to your service, please update your payment method.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${updateUrl}" 
               style="background: #ef4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
              Update Payment Method
            </a>
          </div>
          
          <p>If you have any questions, please contact our support team.</p>
          
          <p>Best regards,<br>The QuickCapt Team</p>
        </div>
      `,
      text: `Hi ${name},\n\nWe were unable to process your payment. Please update your payment method: ${updateUrl}\n\nBest regards,\nThe QuickCapt Team`
    };
  }

  /**
   * Test email configuration
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      logger.info('Email service connection verified');
      return true;
    } catch (error) {
      logger.error('Email service connection failed:', error);
      return false;
    }
  }
}

export default EmailService;
