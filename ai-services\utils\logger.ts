import winston from 'winston';
import path from 'path';
import fs from 'fs';

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let msg = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
      msg += ` ${JSON.stringify(meta, null, 2)}`;
    }
    return msg;
  })
);

// Create logger instance
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: {
    service: 'quickcapt-ai-services',
    environment: process.env.NODE_ENV || 'development'
  },
  transports: [
    // Error log file
    new winston.transports.File({
      filename: path.join(logsDir, 'ai-error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),

    // Combined log file
    new winston.transports.File({
      filename: path.join(logsDir, 'ai-combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),

    // Processing log file
    new winston.transports.File({
      filename: path.join(logsDir, 'ai-processing.log'),
      level: 'info',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  ],
  
  // Handle exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'ai-exceptions.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  ],
  
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'ai-rejections.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  ]
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: 'debug'
  }));
}

// Add console transport for production with limited output
if (process.env.NODE_ENV === 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    ),
    level: 'info'
  }));
}

// Utility functions for structured logging
export const aiLoggers = {
  // Transcription events
  transcription: {
    start: (audioFileId: string, options: any) => {
      logger.info('Transcription started', {
        event: 'transcription.start',
        audioFileId,
        options,
        timestamp: new Date().toISOString()
      });
    },
    
    progress: (audioFileId: string, progress: number, stage: string) => {
      logger.info('Transcription progress', {
        event: 'transcription.progress',
        audioFileId,
        progress,
        stage,
        timestamp: new Date().toISOString()
      });
    },
    
    complete: (audioFileId: string, duration: number, confidence: number) => {
      logger.info('Transcription completed', {
        event: 'transcription.complete',
        audioFileId,
        duration,
        confidence,
        timestamp: new Date().toISOString()
      });
    },
    
    error: (audioFileId: string, error: string, stage: string) => {
      logger.error('Transcription failed', {
        event: 'transcription.error',
        audioFileId,
        error,
        stage,
        timestamp: new Date().toISOString()
      });
    }
  },

  // Analysis events
  analysis: {
    start: (textLength: number, analysisType: string) => {
      logger.info('Analysis started', {
        event: 'analysis.start',
        textLength,
        analysisType,
        timestamp: new Date().toISOString()
      });
    },
    
    complete: (analysisType: string, duration: number, confidence: number) => {
      logger.info('Analysis completed', {
        event: 'analysis.complete',
        analysisType,
        duration,
        confidence,
        timestamp: new Date().toISOString()
      });
    },
    
    error: (analysisType: string, error: string) => {
      logger.error('Analysis failed', {
        event: 'analysis.error',
        analysisType,
        error,
        timestamp: new Date().toISOString()
      });
    }
  },

  // Diarization events
  diarization: {
    start: (audioFileId: string, options: any) => {
      logger.info('Speaker diarization started', {
        event: 'diarization.start',
        audioFileId,
        options,
        timestamp: new Date().toISOString()
      });
    },
    
    complete: (audioFileId: string, speakerCount: number, confidence: number, duration: number) => {
      logger.info('Speaker diarization completed', {
        event: 'diarization.complete',
        audioFileId,
        speakerCount,
        confidence,
        duration,
        timestamp: new Date().toISOString()
      });
    },
    
    error: (audioFileId: string, error: string) => {
      logger.error('Speaker diarization failed', {
        event: 'diarization.error',
        audioFileId,
        error,
        timestamp: new Date().toISOString()
      });
    }
  },

  // Performance events
  performance: {
    apiCall: (service: string, endpoint: string, duration: number, tokens?: number) => {
      logger.info('API call performance', {
        event: 'performance.api_call',
        service,
        endpoint,
        duration,
        tokens,
        timestamp: new Date().toISOString()
      });
    },
    
    processing: (operation: string, inputSize: number, duration: number) => {
      logger.info('Processing performance', {
        event: 'performance.processing',
        operation,
        inputSize,
        duration,
        timestamp: new Date().toISOString()
      });
    }
  },

  // System events
  system: {
    startup: (service: string, port?: number) => {
      logger.info('AI service started', {
        event: 'system.startup',
        service,
        port,
        timestamp: new Date().toISOString()
      });
    },
    
    shutdown: (service: string, reason: string) => {
      logger.info('AI service shutdown', {
        event: 'system.shutdown',
        service,
        reason,
        timestamp: new Date().toISOString()
      });
    },
    
    resourceUsage: (service: string, memory: number, cpu: number) => {
      logger.info('Resource usage', {
        event: 'system.resource_usage',
        service,
        memory,
        cpu,
        timestamp: new Date().toISOString()
      });
    }
  }
};

export default logger;
