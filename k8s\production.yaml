# Kubernetes Production Deployment for QuickCapt
apiVersion: v1
kind: Namespace
metadata:
  name: quickcapt-production
  labels:
    name: quickcapt-production
    environment: production

---
# ConfigMap for application configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: quickcapt-config
  namespace: quickcapt-production
data:
  NODE_ENV: "production"
  PORT: "3001"
  DOMAIN: "quickcapt.com"
  FRONTEND_URL: "https://quickcapt.com"
  BACKEND_URL: "https://api.quickcapt.com"
  AI_SERVICES_URL: "https://ai.quickcapt.com"
  LOG_LEVEL: "info"
  LOG_FORMAT: "json"
  ENABLE_ANALYTICS: "true"
  ENABLE_MONITORING: "true"
  ENABLE_CACHING: "true"

---
# Secret for sensitive configuration
apiVersion: v1
kind: Secret
metadata:
  name: quickcapt-secrets
  namespace: quickcapt-production
type: Opaque
stringData:
  DATABASE_URL: "********************************************/quickcapt"
  REDIS_URL: "redis://redis:6379/0"
  JWT_SECRET: "your-super-secure-jwt-secret"
  JWT_REFRESH_SECRET: "your-super-secure-refresh-secret"
  OPENAI_API_KEY: "sk-your-openai-api-key"
  STRIPE_SECRET_KEY: "sk_live_your-stripe-secret-key"
  STRIPE_WEBHOOK_SECRET: "whsec_your-webhook-secret"
  SMTP_PASS: "your-sendgrid-api-key"

---
# Backend Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: quickcapt-backend
  namespace: quickcapt-production
  labels:
    app: quickcapt-backend
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: quickcapt-backend
  template:
    metadata:
      labels:
        app: quickcapt-backend
        version: v1
    spec:
      containers:
      - name: backend
        image: gcr.io/quickcapt-production/quickcapt-backend:latest
        ports:
        - containerPort: 3001
        envFrom:
        - configMapRef:
            name: quickcapt-config
        - secretRef:
            name: quickcapt-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health/ready
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: uploads
          mountPath: /tmp/uploads
      volumes:
      - name: uploads
        emptyDir: {}

---
# AI Services Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: quickcapt-ai-services
  namespace: quickcapt-production
  labels:
    app: quickcapt-ai-services
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: quickcapt-ai-services
  template:
    metadata:
      labels:
        app: quickcapt-ai-services
        version: v1
    spec:
      containers:
      - name: ai-services
        image: gcr.io/quickcapt-production/quickcapt-ai-services:latest
        ports:
        - containerPort: 3002
        envFrom:
        - configMapRef:
            name: quickcapt-config
        - secretRef:
            name: quickcapt-secrets
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3002
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 3002
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3

---
# Backend Service
apiVersion: v1
kind: Service
metadata:
  name: quickcapt-backend-service
  namespace: quickcapt-production
  labels:
    app: quickcapt-backend
spec:
  selector:
    app: quickcapt-backend
  ports:
  - port: 80
    targetPort: 3001
    protocol: TCP
  type: ClusterIP

---
# AI Services Service
apiVersion: v1
kind: Service
metadata:
  name: quickcapt-ai-services-service
  namespace: quickcapt-production
  labels:
    app: quickcapt-ai-services
spec:
  selector:
    app: quickcapt-ai-services
  ports:
  - port: 80
    targetPort: 3002
    protocol: TCP
  type: ClusterIP

---
# Ingress for external access
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: quickcapt-ingress
  namespace: quickcapt-production
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.quickcapt.com
    - ai.quickcapt.com
    secretName: quickcapt-tls
  rules:
  - host: api.quickcapt.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: quickcapt-backend-service
            port:
              number: 80
  - host: ai.quickcapt.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: quickcapt-ai-services-service
            port:
              number: 80

---
# Horizontal Pod Autoscaler for Backend
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: quickcapt-backend-hpa
  namespace: quickcapt-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: quickcapt-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

---
# Horizontal Pod Autoscaler for AI Services
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: quickcapt-ai-services-hpa
  namespace: quickcapt-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: quickcapt-ai-services
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 85

---
# Network Policy for security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: quickcapt-network-policy
  namespace: quickcapt-production
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: nginx-ingress
    ports:
    - protocol: TCP
      port: 3001
    - protocol: TCP
      port: 3002
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 9200  # Elasticsearch
    - protocol: TCP
      port: 443   # HTTPS
    - protocol: TCP
      port: 80    # HTTP
    - protocol: UDP
      port: 53    # DNS

---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: quickcapt-backend-pdb
  namespace: quickcapt-production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: quickcapt-backend

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: quickcapt-ai-services-pdb
  namespace: quickcapt-production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: quickcapt-ai-services
