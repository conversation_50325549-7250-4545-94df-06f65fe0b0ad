# Production Environment Configuration for QuickCapt
# DO NOT commit this file to version control
# Copy to .env and update with your production values

# Environment
NODE_ENV=production
PORT=3001

# Domain and URLs
DOMAIN=quickcapt.com
FRONTEND_URL=https://quickcapt.com
BACKEND_URL=https://api.quickcapt.com
AI_SERVICES_URL=https://ai.quickcapt.com

# Database Configuration
DATABASE_URL=****************************************/quickcapt_production
DB_SSL=true
DB_MAX_CONNECTIONS=20
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=10000

# Redis Configuration
REDIS_URL=redis://username:password@host:6379/0
REDIS_KEY_PREFIX=quickcapt:prod:
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=100

# Elasticsearch Configuration
ELASTICSEARCH_URL=***********************************
ELASTICSEARCH_INDEX_PREFIX=quickcapt_prod

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key-here
JWT_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.3

# Whisper Configuration
WHISPER_MODEL=whisper-1
WHISPER_LANGUAGE=auto
WHISPER_RESPONSE_FORMAT=json

# File Upload Configuration
MAX_FILE_SIZE=104857600
UPLOAD_PATH=/tmp/uploads
ALLOWED_AUDIO_FORMATS=audio/wav,audio/mp3,audio/mpeg,audio/m4a,audio/webm,audio/ogg,audio/flac

# AWS S3 Configuration (for file storage)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=quickcapt-production-storage
AWS_S3_REGION=us-east-1

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_live_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_live_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
STRIPE_PRO_MONTHLY_PRICE_ID=price_your-pro-monthly-price-id
STRIPE_PRO_YEARLY_PRICE_ID=price_your-pro-yearly-price-id
STRIPE_TEAM_MONTHLY_PRICE_ID=price_your-team-monthly-price-id

# Email Configuration (SendGrid)
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME=QuickCapt

# CORS Configuration
CORS_ORIGIN=https://quickcapt.com
ALLOWED_ORIGINS=https://quickcapt.com,https://www.quickcapt.com,https://app.quickcapt.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AUTH_MAX=5
RATE_LIMIT_UPLOAD_MAX=10
RATE_LIMIT_TRANSCRIPTION_MAX=20
RATE_LIMIT_SEARCH_MAX=60

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-super-secure-session-secret
HELMET_CSP_ENABLED=true

# Monitoring and Logging
LOG_LEVEL=info
LOG_FORMAT=json
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_TRACKING=true

# Sentry Configuration (Error Tracking)
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=production
SENTRY_RELEASE=1.0.0

# Google Analytics
GA_TRACKING_ID=G-XXXXXXXXXX

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_MONITORING=true
ENABLE_CACHING=true
ENABLE_RATE_LIMITING=true
ENABLE_COMPRESSION=true
ENABLE_HELMET=true

# Performance
COMPRESSION_LEVEL=6
CACHE_TTL=3600
STATIC_CACHE_TTL=86400

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30000
WS_MAX_CONNECTIONS=1000
WS_TIMEOUT=60000

# AI Processing
AI_QUEUE_CONCURRENCY=5
AI_PROCESSING_TIMEOUT=300000
AI_RETRY_ATTEMPTS=3

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=quickcapt-production-backups

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/quickcapt.crt
SSL_KEY_PATH=/etc/ssl/private/quickcapt.key
FORCE_HTTPS=true

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Metrics Configuration
METRICS_ENABLED=true
METRICS_PORT=9090
METRICS_PATH=/metrics

# Docker Configuration
DOCKER_REGISTRY=gcr.io/quickcapt-production
DOCKER_TAG=latest

# Kubernetes Configuration
K8S_NAMESPACE=quickcapt-production
K8S_SERVICE_ACCOUNT=quickcapt-sa

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=quickcapt-production
GOOGLE_APPLICATION_CREDENTIALS=/etc/gcp/service-account.json
GCP_STORAGE_BUCKET=quickcapt-production-storage
GCP_REGION=us-central1

# Cloudflare Configuration (if using)
CLOUDFLARE_ZONE_ID=your-cloudflare-zone-id
CLOUDFLARE_API_TOKEN=your-cloudflare-api-token

# CDN Configuration
CDN_URL=https://cdn.quickcapt.com
STATIC_URL=https://static.quickcapt.com

# API Versioning
API_VERSION=v1
API_DEPRECATION_NOTICE=false

# Third-party Integrations
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your/discord/webhook

# Legal and Compliance
PRIVACY_POLICY_URL=https://quickcapt.com/privacy
TERMS_OF_SERVICE_URL=https://quickcapt.com/terms
GDPR_COMPLIANCE=true
CCPA_COMPLIANCE=true

# Support Configuration
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE=******-QUICKCAPT
SUPPORT_CHAT_ENABLED=true
