import winston from 'winston';
import path from 'path';
import fs from 'fs';

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let msg = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
      msg += ` ${JSON.stringify(meta, null, 2)}`;
    }
    return msg;
  })
);

// Create logger instance
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: {
    service: 'quickcapt-backend',
    environment: process.env.NODE_ENV || 'development'
  },
  transports: [
    // Error log file
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),

    // Combined log file
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),

    // Access log file
    new winston.transports.File({
      filename: path.join(logsDir, 'access.log'),
      level: 'http',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  ],
  
  // Handle exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  ],
  
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  ]
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: 'debug'
  }));
}

// Add console transport for production with limited output
if (process.env.NODE_ENV === 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    ),
    level: 'info'
  }));
}

// Create stream for Morgan HTTP logging
export const logStream = {
  write: (message: string) => {
    logger.http(message.trim());
  }
};

// Utility functions for structured logging
export const loggers = {
  // Authentication events
  auth: {
    login: (userId: string, email: string, ip: string) => {
      logger.info('User login successful', {
        event: 'auth.login',
        userId,
        email,
        ip,
        timestamp: new Date().toISOString()
      });
    },
    
    loginFailed: (email: string, ip: string, reason: string) => {
      logger.warn('User login failed', {
        event: 'auth.login_failed',
        email,
        ip,
        reason,
        timestamp: new Date().toISOString()
      });
    },
    
    logout: (userId: string, email: string) => {
      logger.info('User logout', {
        event: 'auth.logout',
        userId,
        email,
        timestamp: new Date().toISOString()
      });
    },
    
    tokenRefresh: (userId: string) => {
      logger.info('Token refresh', {
        event: 'auth.token_refresh',
        userId,
        timestamp: new Date().toISOString()
      });
    }
  },

  // Audio processing events
  audio: {
    upload: (userId: string, filename: string, size: number) => {
      logger.info('Audio file uploaded', {
        event: 'audio.upload',
        userId,
        filename,
        size,
        timestamp: new Date().toISOString()
      });
    },
    
    transcriptionStart: (userId: string, audioFileId: string) => {
      logger.info('Transcription started', {
        event: 'audio.transcription_start',
        userId,
        audioFileId,
        timestamp: new Date().toISOString()
      });
    },
    
    transcriptionComplete: (userId: string, audioFileId: string, duration: number) => {
      logger.info('Transcription completed', {
        event: 'audio.transcription_complete',
        userId,
        audioFileId,
        duration,
        timestamp: new Date().toISOString()
      });
    },
    
    transcriptionError: (userId: string, audioFileId: string, error: string) => {
      logger.error('Transcription failed', {
        event: 'audio.transcription_error',
        userId,
        audioFileId,
        error,
        timestamp: new Date().toISOString()
      });
    }
  },

  // API events
  api: {
    request: (method: string, path: string, userId?: string, ip?: string) => {
      logger.http('API request', {
        event: 'api.request',
        method,
        path,
        userId,
        ip,
        timestamp: new Date().toISOString()
      });
    },
    
    error: (method: string, path: string, error: string, userId?: string) => {
      logger.error('API error', {
        event: 'api.error',
        method,
        path,
        error,
        userId,
        timestamp: new Date().toISOString()
      });
    }
  },

  // Security events
  security: {
    rateLimitExceeded: (identifier: string, path: string, limit: number) => {
      logger.warn('Rate limit exceeded', {
        event: 'security.rate_limit_exceeded',
        identifier,
        path,
        limit,
        timestamp: new Date().toISOString()
      });
    },
    
    suspiciousActivity: (userId: string, activity: string, details: any) => {
      logger.warn('Suspicious activity detected', {
        event: 'security.suspicious_activity',
        userId,
        activity,
        details,
        timestamp: new Date().toISOString()
      });
    },
    
    unauthorizedAccess: (path: string, ip: string, reason: string) => {
      logger.warn('Unauthorized access attempt', {
        event: 'security.unauthorized_access',
        path,
        ip,
        reason,
        timestamp: new Date().toISOString()
      });
    }
  },

  // System events
  system: {
    startup: (port: number, environment: string) => {
      logger.info('Server started', {
        event: 'system.startup',
        port,
        environment,
        timestamp: new Date().toISOString()
      });
    },
    
    shutdown: (reason: string) => {
      logger.info('Server shutdown', {
        event: 'system.shutdown',
        reason,
        timestamp: new Date().toISOString()
      });
    },
    
    databaseConnection: (status: 'connected' | 'disconnected' | 'error', details?: string) => {
      logger.info('Database connection status', {
        event: 'system.database_connection',
        status,
        details,
        timestamp: new Date().toISOString()
      });
    }
  }
};

export default logger;
