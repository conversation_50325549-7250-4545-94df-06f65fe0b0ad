#!/bin/bash

# QuickCapt Production Deployment Script
# This script handles production deployment with zero-downtime

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DEPLOY_ENV=${1:-production}
BACKUP_RETENTION_DAYS=7
HEALTH_CHECK_TIMEOUT=300
ROLLBACK_ON_FAILURE=true

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Validate deployment environment
validate_environment() {
    log_info "Validating deployment environment: $DEPLOY_ENV"
    
    # Check required commands
    local required_commands=("docker" "docker-compose" "git")
    for cmd in "${required_commands[@]}"; do
        if ! command_exists "$cmd"; then
            log_error "$cmd is required but not installed"
            exit 1
        fi
    done
    
    # Check environment file
    if [ ! -f ".env.$DEPLOY_ENV" ]; then
        log_error "Environment file .env.$DEPLOY_ENV not found"
        exit 1
    fi
    
    # Check Docker daemon
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker daemon is not running"
        exit 1
    fi
    
    log_success "Environment validation completed"
}

# Create backup
create_backup() {
    log_info "Creating backup before deployment..."
    
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Backup database
    if docker-compose ps postgres | grep -q "Up"; then
        log_info "Backing up PostgreSQL database..."
        docker-compose exec -T postgres pg_dump -U quickcapt quickcapt > "$backup_dir/database.sql"
    fi
    
    # Backup uploaded files
    if [ -d "uploads" ]; then
        log_info "Backing up uploaded files..."
        tar -czf "$backup_dir/uploads.tar.gz" uploads/
    fi
    
    # Backup configuration
    cp .env "$backup_dir/env.backup" 2>/dev/null || true
    cp docker-compose.yml "$backup_dir/docker-compose.backup"
    
    # Clean old backups
    find backups/ -type d -mtime +$BACKUP_RETENTION_DAYS -exec rm -rf {} + 2>/dev/null || true
    
    echo "$backup_dir" > .last_backup
    log_success "Backup created: $backup_dir"
}

# Build images
build_images() {
    log_info "Building Docker images..."
    
    # Set build arguments
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1
    
    # Build all services
    docker-compose -f docker-compose.yml build --no-cache --parallel
    
    # Tag images with version
    local version=$(git rev-parse --short HEAD)
    docker tag quickcapt_frontend:latest quickcapt_frontend:$version
    docker tag quickcapt_backend:latest quickcapt_backend:$version
    docker tag quickcapt_ai-services:latest quickcapt_ai-services:$version
    
    log_success "Docker images built successfully"
}

# Run tests
run_tests() {
    log_info "Running tests before deployment..."
    
    # Frontend tests
    log_info "Running frontend tests..."
    docker-compose run --rm frontend npm test -- --coverage --watchAll=false
    
    # Backend tests
    log_info "Running backend tests..."
    docker-compose run --rm backend npm test -- --coverage
    
    # Integration tests
    log_info "Running integration tests..."
    docker-compose -f docker-compose.test.yml up --abort-on-container-exit
    docker-compose -f docker-compose.test.yml down
    
    log_success "All tests passed"
}

# Deploy services
deploy_services() {
    log_info "Deploying services..."
    
    # Copy environment file
    cp ".env.$DEPLOY_ENV" .env
    
    # Start infrastructure services first
    log_info "Starting infrastructure services..."
    docker-compose up -d postgres redis elasticsearch
    
    # Wait for infrastructure to be ready
    wait_for_services "postgres redis elasticsearch"
    
    # Run database migrations
    log_info "Running database migrations..."
    docker-compose run --rm backend npm run db:migrate
    
    # Deploy application services with rolling update
    log_info "Deploying application services..."
    
    # Deploy backend first
    docker-compose up -d --no-deps backend
    wait_for_service "backend" "http://localhost:3001/health"
    
    # Deploy AI services
    docker-compose up -d --no-deps ai-services
    wait_for_service "ai-services" "http://localhost:3002/health"
    
    # Deploy frontend last
    docker-compose up -d --no-deps frontend
    wait_for_service "frontend" "http://localhost:3000"
    
    # Start monitoring services if enabled
    if [ "$DEPLOY_ENV" = "production" ]; then
        docker-compose --profile monitoring up -d
    fi
    
    log_success "Services deployed successfully"
}

# Wait for services to be ready
wait_for_services() {
    local services="$1"
    log_info "Waiting for services to be ready: $services"
    
    for service in $services; do
        for i in {1..60}; do
            if docker-compose ps "$service" | grep -q "Up"; then
                log_success "$service is ready"
                break
            fi
            if [ $i -eq 60 ]; then
                log_error "$service failed to start"
                return 1
            fi
            sleep 5
        done
    done
}

# Wait for specific service health check
wait_for_service() {
    local service="$1"
    local health_url="$2"
    log_info "Waiting for $service health check..."
    
    for i in {1..60}; do
        if curl -f "$health_url" >/dev/null 2>&1; then
            log_success "$service health check passed"
            return 0
        fi
        if [ $i -eq 60 ]; then
            log_error "$service health check failed"
            return 1
        fi
        sleep 5
    done
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check all services are running
    local services=("frontend" "backend" "ai-services" "postgres" "redis" "elasticsearch")
    for service in "${services[@]}"; do
        if ! docker-compose ps "$service" | grep -q "Up"; then
            log_error "$service is not running"
            return 1
        fi
    done
    
    # Check health endpoints
    local health_checks=(
        "http://localhost:3000"
        "http://localhost:3001/health"
        "http://localhost:3002/health"
    )
    
    for url in "${health_checks[@]}"; do
        if ! curl -f "$url" >/dev/null 2>&1; then
            log_error "Health check failed: $url"
            return 1
        fi
    done
    
    # Check database connectivity
    if ! docker-compose exec -T postgres pg_isready -U quickcapt -d quickcapt >/dev/null 2>&1; then
        log_error "Database connectivity check failed"
        return 1
    fi
    
    # Check Redis connectivity
    if ! docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
        log_error "Redis connectivity check failed"
        return 1
    fi
    
    log_success "Deployment verification completed"
}

# Rollback deployment
rollback_deployment() {
    log_warning "Rolling back deployment..."
    
    if [ -f .last_backup ]; then
        local backup_dir=$(cat .last_backup)
        
        # Stop current services
        docker-compose down
        
        # Restore database
        if [ -f "$backup_dir/database.sql" ]; then
            log_info "Restoring database..."
            docker-compose up -d postgres
            sleep 10
            docker-compose exec -T postgres psql -U quickcapt -d quickcapt < "$backup_dir/database.sql"
        fi
        
        # Restore files
        if [ -f "$backup_dir/uploads.tar.gz" ]; then
            log_info "Restoring uploaded files..."
            rm -rf uploads/
            tar -xzf "$backup_dir/uploads.tar.gz"
        fi
        
        # Restore configuration
        if [ -f "$backup_dir/env.backup" ]; then
            cp "$backup_dir/env.backup" .env
        fi
        
        # Start services with previous version
        docker-compose up -d
        
        log_success "Rollback completed"
    else
        log_error "No backup found for rollback"
        exit 1
    fi
}

# Cleanup old resources
cleanup() {
    log_info "Cleaning up old resources..."
    
    # Remove unused Docker images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    # Remove unused networks
    docker network prune -f
    
    log_success "Cleanup completed"
}

# Send deployment notification
send_notification() {
    local status="$1"
    local message="$2"
    
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"QuickCapt Deployment $status: $message\"}" \
            "$SLACK_WEBHOOK_URL" >/dev/null 2>&1 || true
    fi
    
    if [ -n "$DISCORD_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"content\":\"QuickCapt Deployment $status: $message\"}" \
            "$DISCORD_WEBHOOK_URL" >/dev/null 2>&1 || true
    fi
}

# Main deployment function
main() {
    local start_time=$(date +%s)
    
    log_info "🚀 Starting QuickCapt deployment to $DEPLOY_ENV..."
    
    # Trap errors for rollback
    if [ "$ROLLBACK_ON_FAILURE" = true ]; then
        trap 'log_error "Deployment failed, initiating rollback..."; rollback_deployment; exit 1' ERR
    fi
    
    validate_environment
    create_backup
    build_images
    
    if [ "$DEPLOY_ENV" != "production" ] || [ "${SKIP_TESTS:-false}" != "true" ]; then
        run_tests
    fi
    
    deploy_services
    verify_deployment
    cleanup
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_success "🎉 Deployment completed successfully in ${duration}s"
    send_notification "SUCCESS" "Deployment to $DEPLOY_ENV completed in ${duration}s"
    
    echo
    echo "Deployment Summary:"
    echo "  Environment: $DEPLOY_ENV"
    echo "  Duration: ${duration}s"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend: http://localhost:3001"
    echo "  AI Services: http://localhost:3002"
    echo
}

# Show usage
usage() {
    echo "Usage: $0 [environment]"
    echo
    echo "Environments:"
    echo "  development  - Deploy to development environment"
    echo "  staging      - Deploy to staging environment"
    echo "  production   - Deploy to production environment (default)"
    echo
    echo "Environment variables:"
    echo "  SKIP_TESTS=true          - Skip running tests"
    echo "  ROLLBACK_ON_FAILURE=false - Disable automatic rollback"
    echo "  SLACK_WEBHOOK_URL        - Slack notification webhook"
    echo "  DISCORD_WEBHOOK_URL      - Discord notification webhook"
    echo
}

# Handle command line arguments
case "${1:-}" in
    -h|--help)
        usage
        exit 0
        ;;
    development|staging|production|"")
        main "$@"
        ;;
    *)
        log_error "Invalid environment: $1"
        usage
        exit 1
        ;;
esac
