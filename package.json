{"name": "quickcapt", "version": "1.0.0", "description": "AI-Powered Note Taking Revolution - Transform your audio recordings into intelligent, searchable notes with real-time transcription, AI insights, and collaborative features.", "main": "index.js", "scripts": {"install:all": "npm install && cd frontend && npm install && cd ../backend && npm install && cd ../ai-services && npm install && cd ../shared && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:ai-services\"", "dev:frontend": "cd frontend && npm start", "dev:backend": "cd backend && npm run dev", "dev:ai-services": "cd ai-services && npm run dev", "build": "npm run build:frontend && npm run build:backend && npm run build:ai-services", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build:ai-services": "cd ai-services && npm run build", "test": "npm run test:frontend && npm run test:backend && npm run test:ai-services", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "test:ai-services": "cd ai-services && npm test", "test:e2e": "cd frontend && npm run test:e2e", "test:coverage": "npm run test:coverage:frontend && npm run test:coverage:backend", "test:coverage:frontend": "cd frontend && npm run test:coverage", "test:coverage:backend": "cd backend && npm run test:coverage", "lint": "npm run lint:frontend && npm run lint:backend && npm run lint:ai-services", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "lint:ai-services": "cd ai-services && npm run lint", "lint:fix": "npm run lint:fix:frontend && npm run lint:fix:backend && npm run lint:fix:ai-services", "lint:fix:frontend": "cd frontend && npm run lint:fix", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:ai-services": "cd ai-services && npm run lint:fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "type-check": "npm run type-check:frontend && npm run type-check:backend && npm run type-check:ai-services", "type-check:frontend": "cd frontend && npm run type-check", "type-check:backend": "cd backend && npm run type-check", "type-check:ai-services": "cd ai-services && npm run type-check", "clean": "npm run clean:frontend && npm run clean:backend && npm run clean:ai-services", "clean:frontend": "cd frontend && rm -rf build node_modules", "clean:backend": "cd backend && rm -rf dist node_modules", "clean:ai-services": "cd ai-services && rm -rf dist node_modules", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down -v --rmi all --remove-orphans", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d", "k8s:deploy": "kubectl apply -f k8s/", "k8s:delete": "kubectl delete -f k8s/", "k8s:logs": "kubectl logs -f deployment/quickcapt-backend -n quickcapt-production", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:reset": "cd backend && npm run db:reset", "db:backup": "cd backend && npm run db:backup", "db:restore": "cd backend && npm run db:restore", "setup": "./scripts/setup-dev.sh", "setup:prod": "./scripts/setup-prod.sh", "deploy": "./scripts/deploy.sh", "deploy:staging": "./scripts/deploy.sh staging", "deploy:production": "./scripts/deploy.sh production", "monitor": "npm run monitor:logs", "monitor:logs": "docker-compose logs -f", "monitor:metrics": "open http://localhost:3000/metrics", "security:audit": "npm audit && cd frontend && npm audit && cd ../backend && npm audit && cd ../ai-services && npm audit", "security:fix": "npm audit fix && cd frontend && npm audit fix && cd ../backend && npm audit fix && cd ../ai-services && npm audit fix", "docs:generate": "cd backend && npm run docs:generate", "docs:serve": "cd backend && npm run docs:serve", "release": "npm run build && npm run test && npm run security:audit", "release:patch": "npm version patch && npm run release", "release:minor": "npm version minor && npm run release", "release:major": "npm version major && npm run release"}, "keywords": ["ai", "note-taking", "transcription", "meeting", "voice-to-text", "collaboration", "productivity", "real-time", "speech-recognition", "summarization"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/HectorTa1989"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/QuickCapt.git"}, "bugs": {"url": "https://github.com/HectorTa1989/QuickCapt/issues"}, "homepage": "https://github.com/HectorTa1989/QuickCapt#readme", "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}