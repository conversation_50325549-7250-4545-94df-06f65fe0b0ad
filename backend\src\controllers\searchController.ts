import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { ValidationError } from '../middleware/errorHandler';
import { ApiResponse, SearchResponse, SearchFilters } from '../../shared/types/api';

const prisma = new PrismaClient();

export class SearchController {
  
  /**
   * Search across notes and transcriptions
   */
  public search = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user!.id;
      const {
        q: query,
        type = 'all',
        page = 1,
        limit = 20,
        filters: filtersParam
      } = req.query;

      if (!query || typeof query !== 'string') {
        throw new ValidationError('Search query is required');
      }

      const skip = (Number(page) - 1) * Number(limit);
      const take = Number(limit);

      // Parse filters
      let filters: SearchFilters = {};
      if (filtersParam && typeof filtersParam === 'string') {
        try {
          filters = JSON.parse(filtersParam);
        } catch (error) {
          throw new ValidationError('Invalid filters format');
        }
      }

      const searchResults = await this.performSearch(
        userId,
        query as string,
        type as string,
        skip,
        take,
        filters
      );

      logger.info('Search performed', {
        userId,
        query,
        type,
        resultsCount: searchResults.total
      });

      const response: ApiResponse<SearchResponse> = {
        success: true,
        data: searchResults,
        message: 'Search completed successfully',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get search suggestions based on user's content
   */
  public getSuggestions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user!.id;
      const { q: query, limit = 10 } = req.query;

      if (!query || typeof query !== 'string') {
        throw new ValidationError('Query is required for suggestions');
      }

      const suggestions = await this.generateSuggestions(
        userId,
        query as string,
        Number(limit)
      );

      const response: ApiResponse<{ suggestions: string[] }> = {
        success: true,
        data: { suggestions },
        message: 'Suggestions retrieved successfully',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get search facets for filtering
   */
  public getFacets = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user!.id;

      const [
        categories,
        tags,
        dateRanges
      ] = await Promise.all([
        // Get categories with counts
        prisma.note.groupBy({
          by: ['category'],
          where: { userId, isArchived: false },
          _count: { category: true }
        }),
        
        // Get popular tags
        this.getPopularTags(userId),
        
        // Get date range facets
        this.getDateRangeFacets(userId)
      ]);

      const facets = {
        categories: categories.map(cat => ({
          value: cat.category,
          count: cat._count.category
        })),
        tags: tags,
        dateRanges: dateRanges
      };

      const response: ApiResponse<typeof facets> = {
        success: true,
        data: facets,
        message: 'Search facets retrieved successfully',
        timestamp: new Date().toISOString()
      };

      res.json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Perform the actual search across different content types
   */
  private async performSearch(
    userId: string,
    query: string,
    type: string,
    skip: number,
    take: number,
    filters: SearchFilters
  ): Promise<SearchResponse> {
    const searchTerm = query.toLowerCase();
    
    // Build base where clause
    const baseWhere: any = {
      userId,
      isArchived: false
    };

    // Apply filters
    if (filters.category) {
      baseWhere.category = filters.category;
    }

    if (filters.dateFrom || filters.dateTo) {
      baseWhere.createdAt = {};
      if (filters.dateFrom) baseWhere.createdAt.gte = new Date(filters.dateFrom);
      if (filters.dateTo) baseWhere.createdAt.lte = new Date(filters.dateTo);
    }

    if (filters.tags && filters.tags.length > 0) {
      baseWhere.tags = {
        hasSome: filters.tags
      };
    }

    let notes: any[] = [];
    let transcriptions: any[] = [];
    let totalNotes = 0;
    let totalTranscriptions = 0;

    // Search in notes
    if (type === 'all' || type === 'notes') {
      const noteWhere = {
        ...baseWhere,
        OR: [
          { title: { contains: searchTerm, mode: 'insensitive' } },
          { content: { contains: searchTerm, mode: 'insensitive' } },
          { tags: { has: searchTerm } }
        ]
      };

      [notes, totalNotes] = await Promise.all([
        prisma.note.findMany({
          where: noteWhere,
          skip: type === 'notes' ? skip : 0,
          take: type === 'notes' ? take : Math.floor(take / 2),
          orderBy: { createdAt: 'desc' },
          include: {
            audioFile: {
              select: {
                id: true,
                filename: true,
                duration: true,
                url: true
              }
            },
            user: {
              select: { id: true, name: true }
            }
          }
        }),
        prisma.note.count({ where: noteWhere })
      ]);
    }

    // Search in transcriptions
    if (type === 'all' || type === 'transcriptions') {
      const transcriptionWhere = {
        text: { contains: searchTerm, mode: 'insensitive' },
        audioFile: {
          userId,
          notes: {
            some: baseWhere
          }
        }
      };

      [transcriptions, totalTranscriptions] = await Promise.all([
        prisma.transcription.findMany({
          where: transcriptionWhere,
          skip: type === 'transcriptions' ? skip : 0,
          take: type === 'transcriptions' ? take : Math.floor(take / 2),
          orderBy: { createdAt: 'desc' },
          include: {
            audioFile: {
              select: {
                id: true,
                filename: true,
                duration: true,
                url: true
              }
            },
            notes: {
              select: {
                id: true,
                title: true,
                category: true
              }
            }
          }
        }),
        prisma.transcription.count({ where: transcriptionWhere })
      ]);
    }

    // Combine and rank results
    const results = [
      ...notes.map(note => ({
        id: note.id,
        type: 'note' as const,
        title: note.title,
        content: this.highlightSearchTerm(note.content, searchTerm),
        category: note.category,
        tags: note.tags,
        createdAt: note.createdAt,
        audioFile: note.audioFile,
        relevanceScore: this.calculateRelevanceScore(note, searchTerm)
      })),
      ...transcriptions.map(transcription => ({
        id: transcription.id,
        type: 'transcription' as const,
        title: transcription.notes[0]?.title || 'Untitled',
        content: this.highlightSearchTerm(transcription.text, searchTerm),
        category: transcription.notes[0]?.category || 'OTHER',
        tags: [],
        createdAt: transcription.createdAt,
        audioFile: transcription.audioFile,
        relevanceScore: this.calculateTranscriptionRelevanceScore(transcription, searchTerm)
      }))
    ];

    // Sort by relevance score
    results.sort((a, b) => b.relevanceScore - a.relevanceScore);

    const total = totalNotes + totalTranscriptions;
    const paginatedResults = type === 'all' 
      ? results.slice(skip, skip + take)
      : results;

    return {
      results: paginatedResults,
      pagination: {
        page: Number(skip / take + 1),
        limit: take,
        total,
        totalPages: Math.ceil(total / take)
      },
      facets: {
        categories: [],
        tags: [],
        dateRanges: []
      },
      query: query,
      type: type
    };
  }

  /**
   * Generate search suggestions
   */
  private async generateSuggestions(
    userId: string,
    query: string,
    limit: number
  ): Promise<string[]> {
    const searchTerm = query.toLowerCase();
    
    // Get suggestions from note titles and tags
    const [titleSuggestions, tagSuggestions] = await Promise.all([
      prisma.note.findMany({
        where: {
          userId,
          title: { contains: searchTerm, mode: 'insensitive' },
          isArchived: false
        },
        select: { title: true },
        take: limit
      }),
      
      prisma.note.findMany({
        where: {
          userId,
          tags: { has: searchTerm },
          isArchived: false
        },
        select: { tags: true },
        take: limit
      })
    ]);

    const suggestions = new Set<string>();
    
    // Add title suggestions
    titleSuggestions.forEach(note => {
      if (suggestions.size < limit) {
        suggestions.add(note.title);
      }
    });
    
    // Add tag suggestions
    tagSuggestions.forEach(note => {
      note.tags.forEach(tag => {
        if (suggestions.size < limit && tag.toLowerCase().includes(searchTerm)) {
          suggestions.add(tag);
        }
      });
    });

    return Array.from(suggestions).slice(0, limit);
  }

  /**
   * Get popular tags for faceting
   */
  private async getPopularTags(userId: string): Promise<Array<{ value: string; count: number }>> {
    const notes = await prisma.note.findMany({
      where: { userId, isArchived: false },
      select: { tags: true }
    });

    const tagCounts = new Map<string, number>();
    
    notes.forEach(note => {
      note.tags.forEach(tag => {
        tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
      });
    });

    return Array.from(tagCounts.entries())
      .map(([value, count]) => ({ value, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 20);
  }

  /**
   * Get date range facets
   */
  private async getDateRangeFacets(userId: string): Promise<Array<{ value: string; count: number }>> {
    const now = new Date();
    const ranges = [
      { label: 'Today', start: new Date(now.getFullYear(), now.getMonth(), now.getDate()) },
      { label: 'This Week', start: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) },
      { label: 'This Month', start: new Date(now.getFullYear(), now.getMonth(), 1) },
      { label: 'This Year', start: new Date(now.getFullYear(), 0, 1) }
    ];

    const counts = await Promise.all(
      ranges.map(async range => ({
        value: range.label,
        count: await prisma.note.count({
          where: {
            userId,
            isArchived: false,
            createdAt: { gte: range.start }
          }
        })
      }))
    );

    return counts.filter(range => range.count > 0);
  }

  /**
   * Highlight search terms in content
   */
  private highlightSearchTerm(content: string, searchTerm: string): string {
    if (!content || !searchTerm) return content;
    
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return content.replace(regex, '<mark>$1</mark>');
  }

  /**
   * Calculate relevance score for notes
   */
  private calculateRelevanceScore(note: any, searchTerm: string): number {
    let score = 0;
    const term = searchTerm.toLowerCase();
    
    // Title match (highest weight)
    if (note.title.toLowerCase().includes(term)) {
      score += 10;
    }
    
    // Content match
    const contentMatches = (note.content.toLowerCase().match(new RegExp(term, 'g')) || []).length;
    score += contentMatches * 2;
    
    // Tag match
    if (note.tags.some((tag: string) => tag.toLowerCase().includes(term))) {
      score += 5;
    }
    
    // Recency bonus
    const daysSinceCreated = (Date.now() - new Date(note.createdAt).getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, 5 - daysSinceCreated / 30); // Bonus decreases over time
    
    return score;
  }

  /**
   * Calculate relevance score for transcriptions
   */
  private calculateTranscriptionRelevanceScore(transcription: any, searchTerm: string): number {
    let score = 0;
    const term = searchTerm.toLowerCase();
    
    // Text match frequency
    const textMatches = (transcription.text.toLowerCase().match(new RegExp(term, 'g')) || []).length;
    score += textMatches * 3;
    
    // Confidence bonus
    score += transcription.confidence * 2;
    
    // Recency bonus
    const daysSinceCreated = (Date.now() - new Date(transcription.createdAt).getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, 3 - daysSinceCreated / 30);
    
    return score;
  }
}

export default new SearchController();
