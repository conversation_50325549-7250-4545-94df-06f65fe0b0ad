import { useState, useEffect, useContext, createContext, ReactNode } from 'react';
import { User, AuthResponse, LoginRequest, RegisterRequest } from '../types';
import { api } from '../services/api';
import { STORAGE_KEYS } from '../utils/constants';
import toast from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  updateUser: (userData: Partial<User>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
        const refreshTokenValue = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);

        if (token && refreshTokenValue) {
          // Set token in API headers
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
          
          try {
            // Verify token by fetching user profile
            const response = await api.get('/auth/profile');
            setUser(response.data.user);
          } catch (error) {
            // Token might be expired, try to refresh
            try {
              await refreshToken();
            } catch (refreshError) {
              // Refresh failed, clear auth state
              clearAuthState();
            }
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        clearAuthState();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Set up token refresh interval
  useEffect(() => {
    if (isAuthenticated) {
      // Refresh token every 6 hours
      const interval = setInterval(() => {
        refreshToken().catch(() => {
          // Silent fail, user will be logged out on next API call
        });
      }, 6 * 60 * 60 * 1000);

      return () => clearInterval(interval);
    }
  }, [isAuthenticated]);

  const clearAuthState = () => {
    setUser(null);
    localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    delete api.defaults.headers.common['Authorization'];
  };

  const setAuthState = (authData: AuthResponse) => {
    setUser(authData.user);
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, authData.token);
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, authData.refreshToken);
    api.defaults.headers.common['Authorization'] = `Bearer ${authData.token}`;
  };

  const login = async (credentials: LoginRequest): Promise<void> => {
    try {
      setIsLoading(true);
      const response = await api.post<AuthResponse>('/auth/login', credentials);
      
      if (response.data.success) {
        setAuthState(response.data.data);
        toast.success(`Welcome back, ${response.data.data.user.name}!`);
      } else {
        throw new Error(response.data.error?.message || 'Login failed');
      }
    } catch (error: any) {
      const message = error.response?.data?.error?.message || error.message || 'Login failed';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterRequest): Promise<void> => {
    try {
      setIsLoading(true);
      
      // Validate passwords match
      if (userData.password !== userData.confirmPassword) {
        throw new Error('Passwords do not match');
      }

      const response = await api.post<AuthResponse>('/auth/register', {
        name: userData.name,
        email: userData.email,
        password: userData.password
      });
      
      if (response.data.success) {
        setAuthState(response.data.data);
        toast.success(`Welcome to QuickCapt, ${response.data.data.user.name}!`);
      } else {
        throw new Error(response.data.error?.message || 'Registration failed');
      }
    } catch (error: any) {
      const message = error.response?.data?.error?.message || error.message || 'Registration failed';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      
      // Call logout endpoint to invalidate token on server
      try {
        await api.post('/auth/logout');
      } catch (error) {
        // Continue with logout even if server call fails
        console.warn('Logout API call failed:', error);
      }
      
      clearAuthState();
      toast.success('Logged out successfully');
    } catch (error: any) {
      console.error('Logout error:', error);
      // Clear state anyway
      clearAuthState();
    } finally {
      setIsLoading(false);
    }
  };

  const refreshToken = async (): Promise<void> => {
    try {
      const refreshTokenValue = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
      
      if (!refreshTokenValue) {
        throw new Error('No refresh token available');
      }

      const response = await api.post<AuthResponse>('/auth/refresh', {
        refreshToken: refreshTokenValue
      });
      
      if (response.data.success) {
        setAuthState(response.data.data);
      } else {
        throw new Error(response.data.error?.message || 'Token refresh failed');
      }
    } catch (error: any) {
      console.error('Token refresh error:', error);
      clearAuthState();
      throw error;
    }
  };

  const updateUser = async (userData: Partial<User>): Promise<void> => {
    try {
      setIsLoading(true);
      
      const response = await api.patch<{ user: User }>('/auth/profile', userData);
      
      if (response.data.success) {
        setUser(response.data.data.user);
        toast.success('Profile updated successfully');
      } else {
        throw new Error(response.data.error?.message || 'Profile update failed');
      }
    } catch (error: any) {
      const message = error.response?.data?.error?.message || error.message || 'Profile update failed';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    refreshToken,
    updateUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Hook for protected routes
export const useRequireAuth = () => {
  const { isAuthenticated, isLoading } = useAuth();
  
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      // Redirect to login page
      window.location.href = '/login';
    }
  }, [isAuthenticated, isLoading]);

  return { isAuthenticated, isLoading };
};

// Hook for guest-only routes (login, register)
export const useRequireGuest = () => {
  const { isAuthenticated, isLoading } = useAuth();
  
  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      // Redirect to dashboard
      window.location.href = '/dashboard';
    }
  }, [isAuthenticated, isLoading]);

  return { isAuthenticated, isLoading };
};

// Hook for checking specific permissions
export const usePermissions = () => {
  const { user } = useAuth();
  
  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    // Check subscription features
    return user.subscription.features.includes(permission);
  };

  const canUploadAudio = (): boolean => {
    return hasPermission('audio_upload');
  };

  const canUseAI = (): boolean => {
    return hasPermission('ai_analysis');
  };

  const canCollaborate = (): boolean => {
    return hasPermission('collaboration');
  };

  const canExport = (): boolean => {
    return hasPermission('export');
  };

  const getRemainingMinutes = (): number => {
    if (!user) return 0;
    
    const { monthlyMinutes } = user.subscription.limits;
    if (monthlyMinutes === -1) return Infinity; // Unlimited
    
    // This would typically come from the API
    // For now, return the limit (in a real app, track usage)
    return monthlyMinutes;
  };

  const getStorageUsage = (): { used: number; limit: number } => {
    if (!user) return { used: 0, limit: 0 };
    
    const { storageGB } = user.subscription.limits;
    
    // This would typically come from the API
    return {
      used: 0, // Would be actual usage
      limit: storageGB === -1 ? Infinity : storageGB * 1024 * 1024 * 1024 // Convert GB to bytes
    };
  };

  return {
    hasPermission,
    canUploadAudio,
    canUseAI,
    canCollaborate,
    canExport,
    getRemainingMinutes,
    getStorageUsage
  };
};

export default useAuth;
