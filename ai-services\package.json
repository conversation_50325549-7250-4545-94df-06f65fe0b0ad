{"name": "quickcapt-ai-services", "version": "1.0.0", "description": "QuickCapt AI Services - Speech-to-Text and Analysis Microservices", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "build:watch": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write src/**/*.ts"}, "keywords": ["ai", "speech-to-text", "transcription", "nlp", "analysis", "whisper", "openai"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"openai": "^4.20.1", "axios": "^1.6.2", "form-data": "^4.0.0", "fluent-ffmpeg": "^2.1.2", "node-wav": "^0.0.2", "speaker-diarization": "^1.0.0", "natural": "^6.8.0", "compromise": "^14.10.0", "sentiment": "^5.0.2", "keyword-extractor": "^0.0.25", "franc": "^6.1.0", "winston": "^3.11.0", "dotenv": "^16.3.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "sharp": "^0.33.0", "tmp": "^0.2.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/fluent-ffmpeg": "^2.1.24", "@types/uuid": "^9.0.7", "@types/tmp": "^0.2.6", "@types/jest": "^29.5.8", "typescript": "^5.3.0", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}