import Stripe from 'stripe';
import { logger } from '../utils/logger';
import { UserService } from './userService';

interface SubscriptionPlan {
  id: string;
  name: string;
  priceId: string;
  price: number;
  interval: 'month' | 'year';
  features: string[];
  limits: {
    monthlyMinutes: number;
    storageGB: number;
    collaborators: number;
  };
}

export class PaymentService {
  private stripe: Stripe;
  private userService: UserService;
  private webhookSecret: string;

  // Subscription plans configuration
  private plans: Record<string, SubscriptionPlan> = {
    pro_monthly: {
      id: 'pro_monthly',
      name: 'Pro Monthly',
      priceId: process.env.STRIPE_PRO_MONTHLY_PRICE_ID!,
      price: 15,
      interval: 'month',
      features: [
        'unlimited_recording',
        'advanced_ai_features',
        'speaker_identification',
        'export_options',
        'priority_support'
      ],
      limits: {
        monthlyMinutes: -1, // unlimited
        storageGB: 50,
        collaborators: 5
      }
    },
    pro_yearly: {
      id: 'pro_yearly',
      name: 'Pro Yearly',
      priceId: process.env.STRIPE_PRO_YEARLY_PRICE_ID!,
      price: 150,
      interval: 'year',
      features: [
        'unlimited_recording',
        'advanced_ai_features',
        'speaker_identification',
        'export_options',
        'priority_support'
      ],
      limits: {
        monthlyMinutes: -1,
        storageGB: 50,
        collaborators: 5
      }
    },
    team_monthly: {
      id: 'team_monthly',
      name: 'Team Monthly',
      priceId: process.env.STRIPE_TEAM_MONTHLY_PRICE_ID!,
      price: 30,
      interval: 'month',
      features: [
        'unlimited_recording',
        'advanced_ai_features',
        'speaker_identification',
        'export_options',
        'team_collaboration',
        'advanced_analytics',
        'integrations',
        'admin_controls',
        'priority_support'
      ],
      limits: {
        monthlyMinutes: -1,
        storageGB: 200,
        collaborators: 25
      }
    }
  };

  constructor() {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2023-10-16',
      typescript: true
    });
    this.userService = new UserService();
    this.webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;
  }

  /**
   * Create Stripe customer
   */
  async createCustomer(userId: string, email: string, name: string): Promise<string> {
    try {
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata: {
          userId
        }
      });

      logger.info(`Stripe customer created: ${customer.id} for user: ${userId}`);
      return customer.id;
    } catch (error) {
      logger.error('Failed to create Stripe customer:', error);
      throw new Error('Failed to create customer');
    }
  }

  /**
   * Create checkout session
   */
  async createCheckoutSession(
    userId: string,
    planId: string,
    successUrl: string,
    cancelUrl: string
  ): Promise<{ sessionId: string; url: string }> {
    try {
      const plan = this.plans[planId];
      if (!plan) {
        throw new Error('Invalid plan ID');
      }

      const user = await this.userService.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Get or create Stripe customer
      let customerId = user.stripeCustomerId;
      if (!customerId) {
        customerId = await this.createCustomer(userId, user.email, user.name);
        await this.userService.updateStripeCustomerId(userId, customerId);
      }

      const session = await this.stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ['card'],
        line_items: [
          {
            price: plan.priceId,
            quantity: 1
          }
        ],
        mode: 'subscription',
        success_url: successUrl,
        cancel_url: cancelUrl,
        metadata: {
          userId,
          planId
        },
        subscription_data: {
          metadata: {
            userId,
            planId
          }
        }
      });

      logger.info(`Checkout session created: ${session.id} for user: ${userId}`);

      return {
        sessionId: session.id,
        url: session.url!
      };
    } catch (error) {
      logger.error('Failed to create checkout session:', error);
      throw new Error('Failed to create checkout session');
    }
  }

  /**
   * Create customer portal session
   */
  async createPortalSession(userId: string, returnUrl: string): Promise<{ url: string }> {
    try {
      const user = await this.userService.findById(userId);
      if (!user || !user.stripeCustomerId) {
        throw new Error('Customer not found');
      }

      const session = await this.stripe.billingPortal.sessions.create({
        customer: user.stripeCustomerId,
        return_url: returnUrl
      });

      return { url: session.url };
    } catch (error) {
      logger.error('Failed to create portal session:', error);
      throw new Error('Failed to create portal session');
    }
  }

  /**
   * Handle webhook events
   */
  async handleWebhook(payload: string, signature: string): Promise<void> {
    try {
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        this.webhookSecret
      );

      logger.info(`Processing Stripe webhook: ${event.type}`);

      switch (event.type) {
        case 'checkout.session.completed':
          await this.handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session);
          break;

        case 'customer.subscription.created':
          await this.handleSubscriptionCreated(event.data.object as Stripe.Subscription);
          break;

        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
          break;

        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;

        case 'invoice.payment_succeeded':
          await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;

        case 'invoice.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.Invoice);
          break;

        default:
          logger.info(`Unhandled webhook event: ${event.type}`);
      }
    } catch (error) {
      logger.error('Webhook processing failed:', error);
      throw error;
    }
  }

  /**
   * Handle checkout session completed
   */
  private async handleCheckoutCompleted(session: Stripe.Checkout.Session): Promise<void> {
    const userId = session.metadata?.userId;
    const planId = session.metadata?.planId;

    if (!userId || !planId) {
      logger.error('Missing metadata in checkout session');
      return;
    }

    const plan = this.plans[planId];
    if (!plan) {
      logger.error(`Invalid plan ID: ${planId}`);
      return;
    }

    // Update user subscription
    await this.userService.updateSubscription(userId, {
      type: this.mapPlanToSubscriptionType(planId),
      status: 'active',
      stripeSubscriptionId: session.subscription as string,
      features: plan.features,
      limits: plan.limits
    });

    logger.info(`Subscription activated for user: ${userId}, plan: ${planId}`);
  }

  /**
   * Handle subscription created
   */
  private async handleSubscriptionCreated(subscription: Stripe.Subscription): Promise<void> {
    const userId = subscription.metadata?.userId;
    if (!userId) return;

    await this.userService.updateSubscriptionStatus(userId, 'active');
    logger.info(`Subscription created for user: ${userId}`);
  }

  /**
   * Handle subscription updated
   */
  private async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<void> {
    const userId = subscription.metadata?.userId;
    if (!userId) return;

    const status = this.mapStripeStatusToSubscriptionStatus(subscription.status);
    await this.userService.updateSubscriptionStatus(userId, status);
    
    logger.info(`Subscription updated for user: ${userId}, status: ${status}`);
  }

  /**
   * Handle subscription deleted
   */
  private async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<void> {
    const userId = subscription.metadata?.userId;
    if (!userId) return;

    // Downgrade to free plan
    await this.userService.updateSubscription(userId, {
      type: 'free',
      status: 'cancelled',
      stripeSubscriptionId: null,
      features: ['basic_transcription', 'simple_notes', 'limited_search'],
      limits: {
        monthlyMinutes: 100,
        storageGB: 1,
        collaborators: 0
      }
    });

    logger.info(`Subscription cancelled for user: ${userId}`);
  }

  /**
   * Handle payment succeeded
   */
  private async handlePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    const customerId = invoice.customer as string;
    const user = await this.userService.findByStripeCustomerId(customerId);
    
    if (user) {
      await this.userService.updateSubscriptionStatus(user.id, 'active');
      logger.info(`Payment succeeded for user: ${user.id}`);
    }
  }

  /**
   * Handle payment failed
   */
  private async handlePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    const customerId = invoice.customer as string;
    const user = await this.userService.findByStripeCustomerId(customerId);
    
    if (user) {
      await this.userService.updateSubscriptionStatus(user.id, 'past_due');
      logger.info(`Payment failed for user: ${user.id}`);
    }
  }

  /**
   * Get available plans
   */
  getPlans(): SubscriptionPlan[] {
    return Object.values(this.plans);
  }

  /**
   * Get plan by ID
   */
  getPlan(planId: string): SubscriptionPlan | null {
    return this.plans[planId] || null;
  }

  /**
   * Map plan ID to subscription type
   */
  private mapPlanToSubscriptionType(planId: string): string {
    if (planId.startsWith('pro_')) return 'pro';
    if (planId.startsWith('team_')) return 'team';
    return 'free';
  }

  /**
   * Map Stripe status to subscription status
   */
  private mapStripeStatusToSubscriptionStatus(stripeStatus: string): string {
    switch (stripeStatus) {
      case 'active':
        return 'active';
      case 'canceled':
        return 'cancelled';
      case 'past_due':
        return 'past_due';
      case 'unpaid':
        return 'past_due';
      default:
        return 'cancelled';
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(userId: string): Promise<void> {
    try {
      const user = await this.userService.findById(userId);
      if (!user || !user.stripeSubscriptionId) {
        throw new Error('No active subscription found');
      }

      await this.stripe.subscriptions.update(user.stripeSubscriptionId, {
        cancel_at_period_end: true
      });

      logger.info(`Subscription cancellation scheduled for user: ${userId}`);
    } catch (error) {
      logger.error('Failed to cancel subscription:', error);
      throw new Error('Failed to cancel subscription');
    }
  }

  /**
   * Get subscription details
   */
  async getSubscriptionDetails(userId: string): Promise<any> {
    try {
      const user = await this.userService.findById(userId);
      if (!user || !user.stripeSubscriptionId) {
        return null;
      }

      const subscription = await this.stripe.subscriptions.retrieve(user.stripeSubscriptionId);
      return subscription;
    } catch (error) {
      logger.error('Failed to get subscription details:', error);
      return null;
    }
  }
}

export default PaymentService;
