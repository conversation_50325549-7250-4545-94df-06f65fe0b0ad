# Multi-stage build for AI Services
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Install system dependencies for AI processing
RUN apk update && apk add --no-cache \
    python3 \
    py3-pip \
    make \
    g++ \
    ffmpeg \
    sox \
    && rm -rf /var/cache/apk/*

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build TypeScript
RUN npm run build

# Production stage
FROM node:18-alpine AS production

# Install system dependencies for AI processing
RUN apk update && apk upgrade && apk add --no-cache \
    python3 \
    py3-pip \
    ffmpeg \
    sox \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Install Python packages for AI processing
RUN pip3 install --no-cache-dir \
    numpy \
    scipy \
    librosa \
    soundfile

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built application
COPY --from=builder /app/dist ./dist

# Create temporary directories for audio processing
RUN mkdir -p temp models logs

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -S -D -H -u 1001 -h /app -s /bin/sh -G appgroup -g appgroup appuser && \
    chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3002/health || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "dist/index.js"]

# Development stage
FROM node:18-alpine AS development

# Install system dependencies
RUN apk update && apk add --no-cache \
    python3 \
    py3-pip \
    make \
    g++ \
    ffmpeg \
    sox \
    git \
    curl \
    && rm -rf /var/cache/apk/*

# Install Python packages for AI processing
RUN pip3 install --no-cache-dir \
    numpy \
    scipy \
    librosa \
    soundfile

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Copy source code
COPY . .

# Create directories
RUN mkdir -p temp models logs

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -S -D -H -u 1001 -h /app -s /bin/sh -G appgroup -g appgroup appuser && \
    chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3002/health || exit 1

# Start development server with nodemon
CMD ["npm", "run", "dev"]
