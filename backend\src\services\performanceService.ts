import { PrismaClient } from '@prisma/client';
import { Redis } from 'ioredis';
import { logger } from '../utils/logger';
import LRU from 'lru-cache';

const prisma = new PrismaClient();
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

export interface PerformanceMetrics {
  requestId: string;
  endpoint: string;
  method: string;
  statusCode: number;
  responseTime: number;
  dbQueries: number;
  dbQueryTime: number;
  cacheHits: number;
  cacheMisses: number;
  memoryUsage: number;
  timestamp: Date;
}

export interface CacheConfig {
  ttl: number; // Time to live in seconds
  maxSize: number; // Maximum number of items
  staleWhileRevalidate?: number; // Serve stale data while revalidating
}

export class PerformanceService {
  private memoryCache: LRU<string, any>;
  private performanceMetrics: PerformanceMetrics[] = [];
  private slowQueryThreshold = 1000; // 1 second
  private slowRequestThreshold = 2000; // 2 seconds

  constructor() {
    // Initialize in-memory cache
    this.memoryCache = new LRU({
      max: 1000, // Maximum 1000 items
      ttl: 5 * 60 * 1000, // 5 minutes TTL
    });

    this.initializePerformanceMonitoring();
  }

  /**
   * Initialize performance monitoring
   */
  private initializePerformanceMonitoring(): void {
    // Flush metrics every 5 minutes
    setInterval(() => {
      this.flushMetrics();
    }, 5 * 60 * 1000);

    // Clean up old metrics every hour
    setInterval(() => {
      this.cleanupMetrics();
    }, 60 * 60 * 1000);

    // Optimize database connections every 30 minutes
    setInterval(() => {
      this.optimizeDatabaseConnections();
    }, 30 * 60 * 1000);

    logger.info('Performance monitoring initialized');
  }

  /**
   * Record performance metrics for a request
   */
  recordRequestMetrics(metrics: Omit<PerformanceMetrics, 'timestamp'>): void {
    const fullMetrics: PerformanceMetrics = {
      ...metrics,
      timestamp: new Date()
    };

    this.performanceMetrics.push(fullMetrics);

    // Log slow requests
    if (metrics.responseTime > this.slowRequestThreshold) {
      logger.warn('Slow request detected', {
        endpoint: metrics.endpoint,
        method: metrics.method,
        responseTime: metrics.responseTime,
        dbQueries: metrics.dbQueries,
        dbQueryTime: metrics.dbQueryTime
      });
    }

    // Log slow database queries
    if (metrics.dbQueryTime > this.slowQueryThreshold) {
      logger.warn('Slow database query detected', {
        endpoint: metrics.endpoint,
        dbQueryTime: metrics.dbQueryTime,
        dbQueries: metrics.dbQueries
      });
    }
  }

  /**
   * Get cached data with fallback to database
   */
  async getCachedData<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    config: CacheConfig = { ttl: 300, maxSize: 100 }
  ): Promise<T> {
    const startTime = Date.now();

    try {
      // Try memory cache first
      let data = this.memoryCache.get(key);
      if (data) {
        logger.debug('Memory cache hit', { key, responseTime: Date.now() - startTime });
        return data;
      }

      // Try Redis cache
      const redisData = await redis.get(key);
      if (redisData) {
        data = JSON.parse(redisData);
        // Store in memory cache for faster access
        this.memoryCache.set(key, data);
        logger.debug('Redis cache hit', { key, responseTime: Date.now() - startTime });
        return data;
      }

      // Cache miss - fetch from source
      logger.debug('Cache miss', { key });
      data = await fetchFunction();

      // Store in both caches
      this.memoryCache.set(key, data);
      await redis.setex(key, config.ttl, JSON.stringify(data));

      logger.debug('Data cached', { 
        key, 
        responseTime: Date.now() - startTime,
        dataSize: JSON.stringify(data).length 
      });

      return data;

    } catch (error) {
      logger.error('Cache operation failed', {
        error: error.message,
        key,
        responseTime: Date.now() - startTime
      });
      
      // Fallback to direct fetch
      return await fetchFunction();
    }
  }

  /**
   * Invalidate cache entries
   */
  async invalidateCache(pattern: string): Promise<void> {
    try {
      // Clear from memory cache
      for (const key of this.memoryCache.keys()) {
        if (key.includes(pattern)) {
          this.memoryCache.delete(key);
        }
      }

      // Clear from Redis cache
      const keys = await redis.keys(`*${pattern}*`);
      if (keys.length > 0) {
        await redis.del(...keys);
      }

      logger.info('Cache invalidated', { pattern, keysCleared: keys.length });

    } catch (error) {
      logger.error('Cache invalidation failed', {
        error: error.message,
        pattern
      });
    }
  }

  /**
   * Preload frequently accessed data
   */
  async preloadCache(): Promise<void> {
    try {
      logger.info('Starting cache preload');

      // Preload user statistics
      const userStats = await prisma.user.groupBy({
        by: ['subscriptionType'],
        _count: { subscriptionType: true }
      });
      await this.getCachedData('user_stats', () => Promise.resolve(userStats), { ttl: 3600, maxSize: 1 });

      // Preload popular notes
      const popularNotes = await prisma.note.findMany({
        where: { isPublic: true },
        orderBy: { createdAt: 'desc' },
        take: 50,
        include: {
          user: { select: { id: true, name: true } }
        }
      });
      await this.getCachedData('popular_notes', () => Promise.resolve(popularNotes), { ttl: 1800, maxSize: 1 });

      // Preload system configuration
      const systemConfig = {
        maxFileSize: process.env.MAX_FILE_SIZE || '100MB',
        supportedFormats: process.env.SUPPORTED_FORMATS?.split(',') || ['mp3', 'wav', 'm4a'],
        features: {
          transcription: true,
          analysis: true,
          collaboration: true
        }
      };
      await this.getCachedData('system_config', () => Promise.resolve(systemConfig), { ttl: 7200, maxSize: 1 });

      logger.info('Cache preload completed');

    } catch (error) {
      logger.error('Cache preload failed', {
        error: error.message
      });
    }
  }

  /**
   * Optimize database queries
   */
  async optimizeDatabaseQueries(): Promise<void> {
    try {
      logger.info('Starting database optimization');

      // Analyze slow queries from metrics
      const slowQueries = this.performanceMetrics
        .filter(m => m.dbQueryTime > this.slowQueryThreshold)
        .reduce((acc, metric) => {
          const key = `${metric.method} ${metric.endpoint}`;
          if (!acc[key]) {
            acc[key] = { count: 0, totalTime: 0, avgTime: 0 };
          }
          acc[key].count++;
          acc[key].totalTime += metric.dbQueryTime;
          acc[key].avgTime = acc[key].totalTime / acc[key].count;
          return acc;
        }, {} as Record<string, { count: number; totalTime: number; avgTime: number }>);

      // Log optimization suggestions
      Object.entries(slowQueries).forEach(([endpoint, stats]) => {
        if (stats.count > 5) { // Only suggest optimization for frequently slow queries
          logger.warn('Query optimization needed', {
            endpoint,
            slowQueryCount: stats.count,
            averageTime: stats.avgTime,
            suggestion: 'Consider adding database indexes or optimizing query structure'
          });
        }
      });

      // Update query statistics
      await redis.hset('query_stats', slowQueries);

      logger.info('Database optimization analysis completed');

    } catch (error) {
      logger.error('Database optimization failed', {
        error: error.message
      });
    }
  }

  /**
   * Optimize database connections
   */
  private async optimizeDatabaseConnections(): Promise<void> {
    try {
      // Get connection pool statistics (this would depend on your database setup)
      const connectionStats = {
        active: Math.floor(Math.random() * 20) + 5, // Simulated
        idle: Math.floor(Math.random() * 10) + 2,   // Simulated
        total: 30 // Simulated max connections
      };

      logger.info('Database connection stats', connectionStats);

      // Warn if connection usage is high
      if (connectionStats.active / connectionStats.total > 0.8) {
        logger.warn('High database connection usage', {
          usage: (connectionStats.active / connectionStats.total * 100).toFixed(2) + '%',
          active: connectionStats.active,
          total: connectionStats.total,
          suggestion: 'Consider increasing connection pool size or optimizing query patterns'
        });
      }

    } catch (error) {
      logger.error('Database connection optimization failed', {
        error: error.message
      });
    }
  }

  /**
   * Get performance analytics
   */
  getPerformanceAnalytics(): {
    averageResponseTime: number;
    slowRequestsCount: number;
    cacheHitRate: number;
    databasePerformance: {
      averageQueryTime: number;
      slowQueriesCount: number;
      queriesPerRequest: number;
    };
    topSlowEndpoints: Array<{
      endpoint: string;
      averageResponseTime: number;
      requestCount: number;
    }>;
  } {
    if (this.performanceMetrics.length === 0) {
      return {
        averageResponseTime: 0,
        slowRequestsCount: 0,
        cacheHitRate: 0,
        databasePerformance: {
          averageQueryTime: 0,
          slowQueriesCount: 0,
          queriesPerRequest: 0
        },
        topSlowEndpoints: []
      };
    }

    const totalRequests = this.performanceMetrics.length;
    const totalResponseTime = this.performanceMetrics.reduce((sum, m) => sum + m.responseTime, 0);
    const slowRequests = this.performanceMetrics.filter(m => m.responseTime > this.slowRequestThreshold);
    
    const totalCacheHits = this.performanceMetrics.reduce((sum, m) => sum + m.cacheHits, 0);
    const totalCacheMisses = this.performanceMetrics.reduce((sum, m) => sum + m.cacheMisses, 0);
    const cacheHitRate = totalCacheHits / (totalCacheHits + totalCacheMisses) || 0;

    const totalDbQueryTime = this.performanceMetrics.reduce((sum, m) => sum + m.dbQueryTime, 0);
    const totalDbQueries = this.performanceMetrics.reduce((sum, m) => sum + m.dbQueries, 0);
    const slowDbQueries = this.performanceMetrics.filter(m => m.dbQueryTime > this.slowQueryThreshold);

    // Calculate top slow endpoints
    const endpointStats = this.performanceMetrics.reduce((acc, metric) => {
      const key = `${metric.method} ${metric.endpoint}`;
      if (!acc[key]) {
        acc[key] = { totalTime: 0, count: 0 };
      }
      acc[key].totalTime += metric.responseTime;
      acc[key].count++;
      return acc;
    }, {} as Record<string, { totalTime: number; count: number }>);

    const topSlowEndpoints = Object.entries(endpointStats)
      .map(([endpoint, stats]) => ({
        endpoint,
        averageResponseTime: stats.totalTime / stats.count,
        requestCount: stats.count
      }))
      .sort((a, b) => b.averageResponseTime - a.averageResponseTime)
      .slice(0, 10);

    return {
      averageResponseTime: totalResponseTime / totalRequests,
      slowRequestsCount: slowRequests.length,
      cacheHitRate,
      databasePerformance: {
        averageQueryTime: totalDbQueryTime / totalRequests,
        slowQueriesCount: slowDbQueries.length,
        queriesPerRequest: totalDbQueries / totalRequests
      },
      topSlowEndpoints
    };
  }

  /**
   * Flush metrics to persistent storage
   */
  private async flushMetrics(): Promise<void> {
    try {
      if (this.performanceMetrics.length === 0) return;

      // In production, you would store these in a time-series database
      // like InfluxDB, Prometheus, or CloudWatch
      
      const analytics = this.getPerformanceAnalytics();
      await redis.hset('performance_analytics', {
        timestamp: new Date().toISOString(),
        ...analytics
      });

      logger.info('Performance metrics flushed', {
        metricsCount: this.performanceMetrics.length,
        averageResponseTime: analytics.averageResponseTime,
        cacheHitRate: analytics.cacheHitRate
      });

    } catch (error) {
      logger.error('Failed to flush performance metrics', {
        error: error.message
      });
    }
  }

  /**
   * Clean up old metrics
   */
  private cleanupMetrics(): void {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const initialCount = this.performanceMetrics.length;
    
    this.performanceMetrics = this.performanceMetrics.filter(
      metric => metric.timestamp > oneHourAgo
    );

    const removedCount = initialCount - this.performanceMetrics.length;
    if (removedCount > 0) {
      logger.debug('Performance metrics cleaned up', {
        removedCount,
        remainingCount: this.performanceMetrics.length
      });
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStatistics(): {
    memoryCache: {
      size: number;
      maxSize: number;
      hitRate: number;
    };
    redisCache: {
      connected: boolean;
      memoryUsage?: number;
    };
  } {
    return {
      memoryCache: {
        size: this.memoryCache.size,
        maxSize: this.memoryCache.max,
        hitRate: 0 // LRU cache doesn't provide hit rate by default
      },
      redisCache: {
        connected: redis.status === 'ready'
      }
    };
  }

  /**
   * Warm up cache with essential data
   */
  async warmupCache(): Promise<void> {
    try {
      logger.info('Starting cache warmup');
      await this.preloadCache();
      logger.info('Cache warmup completed');
    } catch (error) {
      logger.error('Cache warmup failed', {
        error: error.message
      });
    }
  }
}

export default new PerformanceService();
