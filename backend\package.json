{"name": "quickcapt-backend", "version": "1.0.0", "description": "QuickCapt Backend - Node.js API Server for AI Note Taking", "main": "dist/app.js", "scripts": {"start": "node dist/app.js", "dev": "nodemon src/app.ts", "build": "tsc", "build:watch": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write src/**/*.ts", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:seed": "ts-node src/utils/seed.ts", "docker:build": "docker build -t quickcapt-backend .", "docker:run": "docker run -p 3001:3001 quickcapt-backend"}, "keywords": ["nodejs", "express", "typescript", "api", "websocket", "audio", "transcription", "ai"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "redis": "^4.6.10", "pg": "^8.11.3", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "dotenv": "^16.3.1", "joi": "^17.11.0", "axios": "^1.6.2", "openai": "^4.20.1", "form-data": "^4.0.0", "fluent-ffmpeg": "^2.1.2", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "sharp": "^0.33.0", "uuid": "^9.0.1", "crypto": "^1.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/pg": "^8.10.9", "@types/joi": "^17.2.3", "@types/fluent-ffmpeg": "^2.1.24", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.0", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/app.ts"]}}