import { useState, useEffect, useCallback } from 'react';
import { Note, CreateNoteRequest, UpdateNoteRequest, PaginatedResponse } from '../types';
import { api } from '../services/api';
import { useNotificationHelpers } from './useNotifications';
import toast from 'react-hot-toast';

interface UseNotesOptions {
  autoFetch?: boolean;
  pageSize?: number;
}

interface NotesState {
  notes: Note[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  total: number;
  page: number;
}

interface NotesFilters {
  search?: string;
  category?: string;
  tags?: string[];
  dateFrom?: string;
  dateTo?: string;
  hasAudio?: boolean;
  isPublic?: boolean;
  collaboratorId?: string;
}

export const useNotes = (options: UseNotesOptions = {}) => {
  const { autoFetch = true, pageSize = 20 } = options;
  const { notifyError } = useNotificationHelpers();

  const [state, setState] = useState<NotesState>({
    notes: [],
    loading: false,
    error: null,
    hasMore: true,
    total: 0,
    page: 1
  });

  const [filters, setFilters] = useState<NotesFilters>({});
  const [recentNotes, setRecentNotes] = useState<Note[]>([]);
  const [favoriteNotes, setFavoriteNotes] = useState<Note[]>([]);
  const [categories, setCategories] = useState<Array<{ name: string; count: number }>>([]);
  const [tags, setTags] = useState<Array<{ name: string; count: number }>>([]);

  // Fetch notes with filters and pagination
  const fetchNotes = useCallback(async (
    page = 1, 
    reset = false, 
    currentFilters = filters
  ) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const params = new URLSearchParams({
        page: page.toString(),
        limit: pageSize.toString(),
        ...currentFilters,
        ...(currentFilters.tags && { tags: currentFilters.tags.join(',') })
      });

      const response = await api.get<PaginatedResponse<Note>>(`/notes?${params}`);
      
      if (response.data.success) {
        const { items, total, hasNext } = response.data.data;
        
        setState(prev => ({
          ...prev,
          notes: reset ? items : [...prev.notes, ...items],
          total,
          hasMore: hasNext,
          page,
          loading: false
        }));
      } else {
        throw new Error(response.data.error?.message || 'Failed to fetch notes');
      }
    } catch (error: any) {
      const message = error.response?.data?.error?.message || error.message || 'Failed to fetch notes';
      setState(prev => ({ ...prev, error: message, loading: false }));
      notifyError('Error', message);
    }
  }, [filters, pageSize, notifyError]);

  // Load more notes (pagination)
  const loadMore = useCallback(() => {
    if (!state.loading && state.hasMore) {
      fetchNotes(state.page + 1, false);
    }
  }, [state.loading, state.hasMore, state.page, fetchNotes]);

  // Refresh notes (reset pagination)
  const refresh = useCallback(() => {
    fetchNotes(1, true);
  }, [fetchNotes]);

  // Update filters and refetch
  const updateFilters = useCallback((newFilters: Partial<NotesFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    fetchNotes(1, true, updatedFilters);
  }, [filters, fetchNotes]);

  // Clear filters
  const clearFilters = useCallback(() => {
    setFilters({});
    fetchNotes(1, true, {});
  }, [fetchNotes]);

  // Create new note
  const createNote = useCallback(async (noteData: CreateNoteRequest): Promise<Note> => {
    try {
      const response = await api.post<{ note: Note }>('/notes', noteData);
      
      if (response.data.success) {
        const newNote = response.data.data.note;
        setState(prev => ({
          ...prev,
          notes: [newNote, ...prev.notes],
          total: prev.total + 1
        }));
        toast.success('Note created successfully');
        return newNote;
      } else {
        throw new Error(response.data.error?.message || 'Failed to create note');
      }
    } catch (error: any) {
      const message = error.response?.data?.error?.message || error.message || 'Failed to create note';
      notifyError('Error', message);
      throw error;
    }
  }, [notifyError]);

  // Update existing note
  const updateNote = useCallback(async (id: string, noteData: UpdateNoteRequest): Promise<Note> => {
    try {
      const response = await api.put<{ note: Note }>(`/notes/${id}`, noteData);
      
      if (response.data.success) {
        const updatedNote = response.data.data.note;
        setState(prev => ({
          ...prev,
          notes: prev.notes.map(note => note.id === id ? updatedNote : note)
        }));
        toast.success('Note updated successfully');
        return updatedNote;
      } else {
        throw new Error(response.data.error?.message || 'Failed to update note');
      }
    } catch (error: any) {
      const message = error.response?.data?.error?.message || error.message || 'Failed to update note';
      notifyError('Error', message);
      throw error;
    }
  }, [notifyError]);

  // Delete note
  const deleteNote = useCallback(async (id: string): Promise<void> => {
    try {
      const response = await api.delete(`/notes/${id}`);
      
      if (response.data.success) {
        setState(prev => ({
          ...prev,
          notes: prev.notes.filter(note => note.id !== id),
          total: prev.total - 1
        }));
        toast.success('Note deleted successfully');
      } else {
        throw new Error(response.data.error?.message || 'Failed to delete note');
      }
    } catch (error: any) {
      const message = error.response?.data?.error?.message || error.message || 'Failed to delete note';
      notifyError('Error', message);
      throw error;
    }
  }, [notifyError]);

  // Get single note by ID
  const getNote = useCallback(async (id: string): Promise<Note> => {
    try {
      const response = await api.get<{ note: Note }>(`/notes/${id}`);
      
      if (response.data.success) {
        return response.data.data.note;
      } else {
        throw new Error(response.data.error?.message || 'Failed to fetch note');
      }
    } catch (error: any) {
      const message = error.response?.data?.error?.message || error.message || 'Failed to fetch note';
      notifyError('Error', message);
      throw error;
    }
  }, [notifyError]);

  // Duplicate note
  const duplicateNote = useCallback(async (id: string): Promise<Note> => {
    try {
      const originalNote = await getNote(id);
      const duplicatedNote = await createNote({
        title: `${originalNote.title} (Copy)`,
        content: originalNote.content,
        category: originalNote.category,
        tags: originalNote.tags,
        isPublic: false
      });
      return duplicatedNote;
    } catch (error) {
      notifyError('Error', 'Failed to duplicate note');
      throw error;
    }
  }, [getNote, createNote, notifyError]);

  // Toggle favorite status
  const toggleFavorite = useCallback(async (id: string): Promise<void> => {
    try {
      const response = await api.post(`/notes/${id}/favorite`);
      
      if (response.data.success) {
        setState(prev => ({
          ...prev,
          notes: prev.notes.map(note => 
            note.id === id 
              ? { ...note, isFavorite: !note.isFavorite }
              : note
          )
        }));
        toast.success('Favorite status updated');
      } else {
        throw new Error(response.data.error?.message || 'Failed to update favorite status');
      }
    } catch (error: any) {
      const message = error.response?.data?.error?.message || error.message || 'Failed to update favorite status';
      notifyError('Error', message);
    }
  }, [notifyError]);

  // Archive/unarchive note
  const toggleArchive = useCallback(async (id: string): Promise<void> => {
    try {
      const response = await api.post(`/notes/${id}/archive`);
      
      if (response.data.success) {
        setState(prev => ({
          ...prev,
          notes: prev.notes.map(note => 
            note.id === id 
              ? { ...note, isArchived: !note.isArchived }
              : note
          )
        }));
        toast.success('Archive status updated');
      } else {
        throw new Error(response.data.error?.message || 'Failed to update archive status');
      }
    } catch (error: any) {
      const message = error.response?.data?.error?.message || error.message || 'Failed to update archive status';
      notifyError('Error', message);
    }
  }, [notifyError]);

  // Fetch recent notes
  const fetchRecentNotes = useCallback(async () => {
    try {
      const response = await api.get<PaginatedResponse<Note>>('/notes?limit=10&sort=updatedAt&order=desc');
      if (response.data.success) {
        setRecentNotes(response.data.data.items);
      }
    } catch (error) {
      console.error('Failed to fetch recent notes:', error);
    }
  }, []);

  // Fetch favorite notes
  const fetchFavoriteNotes = useCallback(async () => {
    try {
      const response = await api.get<PaginatedResponse<Note>>('/notes?filter=favorites&limit=10');
      if (response.data.success) {
        setFavoriteNotes(response.data.data.items);
      }
    } catch (error) {
      console.error('Failed to fetch favorite notes:', error);
    }
  }, []);

  // Fetch categories and tags
  const fetchMetadata = useCallback(async () => {
    try {
      const [categoriesResponse, tagsResponse] = await Promise.all([
        api.get<Array<{ name: string; count: number }>>('/notes/categories'),
        api.get<Array<{ name: string; count: number }>>('/notes/tags')
      ]);

      if (categoriesResponse.data.success) {
        setCategories(categoriesResponse.data.data);
      }
      if (tagsResponse.data.success) {
        setTags(tagsResponse.data.data);
      }
    } catch (error) {
      console.error('Failed to fetch metadata:', error);
    }
  }, []);

  // Auto-fetch on mount
  useEffect(() => {
    if (autoFetch) {
      fetchNotes(1, true);
      fetchRecentNotes();
      fetchFavoriteNotes();
      fetchMetadata();
    }
  }, [autoFetch, fetchNotes, fetchRecentNotes, fetchFavoriteNotes, fetchMetadata]);

  return {
    // State
    notes: state.notes,
    loading: state.loading,
    error: state.error,
    hasMore: state.hasMore,
    total: state.total,
    page: state.page,
    filters,
    recentNotes,
    favoriteNotes,
    categories,
    tags,

    // Actions
    fetchNotes,
    loadMore,
    refresh,
    updateFilters,
    clearFilters,
    createNote,
    updateNote,
    deleteNote,
    getNote,
    duplicateNote,
    toggleFavorite,
    toggleArchive,
    fetchRecentNotes,
    fetchFavoriteNotes,
    fetchMetadata
  };
};

export default useNotes;
