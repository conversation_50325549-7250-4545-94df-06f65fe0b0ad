# QuickCapt API Documentation

## Overview

The QuickCapt API provides comprehensive endpoints for AI-powered note taking, including audio recording, transcription, analysis, and search capabilities.

**Base URL:** `http://localhost:3001/api`

**Authentication:** JWT Bearer Token

## Authentication

### POST /auth/register

Register a new user account.

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "createdAt": "2024-01-01T00:00:00Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_here"
  }
}
```

### POST /auth/login

Authenticate user and receive access token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_here"
  }
}
```

### POST /auth/refresh

Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "refresh_token_here"
}
```

## Notes Management

### GET /notes

Retrieve user's notes with pagination and filtering.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20)
- `category` (string): Filter by category
- `tags` (string): Comma-separated tags
- `search` (string): Search in title and content

**Response:**
```json
{
  "success": true,
  "data": {
    "notes": [
      {
        "id": "note_123",
        "title": "Team Meeting Notes",
        "content": "Discussion about Q4 goals...",
        "summary": "Key decisions made regarding...",
        "category": "meeting",
        "tags": ["team", "q4", "goals"],
        "audioUrl": "https://example.com/audio/123.wav",
        "audioDuration": 1800,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 20,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### POST /notes

Create a new note.

**Request Body:**
```json
{
  "title": "Meeting Notes",
  "content": "Initial content...",
  "category": "meeting",
  "tags": ["team", "planning"],
  "isPublic": false
}
```

### GET /notes/:id

Retrieve a specific note by ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "note": {
      "id": "note_123",
      "title": "Team Meeting Notes",
      "content": "Full content...",
      "transcription": {
        "text": "Full transcription...",
        "segments": [...],
        "speakers": [...]
      },
      "analysis": {
        "summary": "Meeting summary...",
        "keyPoints": [...],
        "actionItems": [...],
        "sentiment": {...}
      }
    }
  }
}
```

### PUT /notes/:id

Update an existing note.

**Request Body:**
```json
{
  "title": "Updated Title",
  "content": "Updated content...",
  "tags": ["updated", "tags"]
}
```

### DELETE /notes/:id

Delete a note.

**Response:**
```json
{
  "success": true,
  "message": "Note deleted successfully"
}
```

## Audio Processing

### POST /audio/upload

Upload audio file for transcription and analysis.

**Request:** Multipart form data
- `audio` (file): Audio file (WAV, MP3, M4A, WebM, OGG)
- `title` (string): Optional title for the note
- `category` (string): Optional category
- `language` (string): Optional language code for transcription

**Response:**
```json
{
  "success": true,
  "data": {
    "audioFile": {
      "id": "audio_123",
      "filename": "recording.wav",
      "size": 1024000,
      "duration": 1800,
      "url": "https://example.com/audio/123.wav"
    },
    "processingId": "proc_123"
  }
}
```

### GET /audio/:id

Retrieve audio file information.

**Response:**
```json
{
  "success": true,
  "data": {
    "audioFile": {
      "id": "audio_123",
      "filename": "recording.wav",
      "originalName": "team-meeting.wav",
      "mimeType": "audio/wav",
      "size": 1024000,
      "duration": 1800,
      "url": "https://example.com/audio/123.wav",
      "waveformData": [0.1, 0.2, 0.3, ...],
      "createdAt": "2024-01-01T00:00:00Z"
    }
  }
}
```

### POST /audio/:id/transcribe

Start transcription process for uploaded audio.

**Request Body:**
```json
{
  "language": "en",
  "speakerIdentification": true,
  "model": "whisper-1"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "transcriptionId": "trans_123",
    "status": "processing",
    "estimatedTime": 120
  }
}
```

### GET /audio/:id/transcription

Get transcription status and results.

**Response:**
```json
{
  "success": true,
  "data": {
    "transcription": {
      "id": "trans_123",
      "text": "Full transcription text...",
      "language": "en",
      "confidence": 0.95,
      "processingStatus": "completed",
      "segments": [
        {
          "id": "seg_1",
          "text": "Hello everyone, welcome to the meeting.",
          "startTime": 0.0,
          "endTime": 3.5,
          "speakerId": "speaker_1",
          "confidence": 0.98
        }
      ],
      "speakers": [
        {
          "id": "speaker_1",
          "name": "John Doe",
          "color": "#3b82f6"
        }
      ]
    }
  }
}
```

## AI Analysis

### POST /notes/:id/analyze

Trigger AI analysis for a note.

**Request Body:**
```json
{
  "features": ["summary", "actionItems", "sentiment", "topics", "entities"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "analysisId": "analysis_123",
    "status": "processing"
  }
}
```

### GET /notes/:id/analysis

Get AI analysis results.

**Response:**
```json
{
  "success": true,
  "data": {
    "analysis": {
      "summary": "This meeting covered quarterly planning...",
      "keyPoints": [
        "Q4 revenue target set at $2M",
        "New product launch scheduled for March",
        "Team expansion approved"
      ],
      "actionItems": [
        {
          "id": "action_1",
          "text": "Prepare Q4 budget proposal",
          "assignee": "John Doe",
          "dueDate": "2024-01-15",
          "priority": "high",
          "status": "pending"
        }
      ],
      "sentiment": {
        "overall": "positive",
        "score": 0.75,
        "segments": [...]
      },
      "topics": [
        {
          "name": "Budget Planning",
          "confidence": 0.89,
          "mentions": 5
        }
      ],
      "entities": [
        {
          "text": "John Doe",
          "type": "person",
          "confidence": 0.95,
          "mentions": [...]
        }
      ]
    }
  }
}
```

## Search

### GET /search

Search across notes with advanced filtering.

**Query Parameters:**
- `q` (string): Search query
- `category` (string): Filter by category
- `tags` (string): Comma-separated tags
- `dateFrom` (string): Start date (ISO format)
- `dateTo` (string): End date (ISO format)
- `hasAudio` (boolean): Filter notes with audio
- `sentiment` (string): Filter by sentiment
- `page` (number): Page number
- `limit` (number): Items per page

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "note": {...},
        "score": 0.95,
        "highlights": [
          {
            "field": "content",
            "text": "...highlighted text...",
            "startIndex": 100,
            "endIndex": 120
          }
        ]
      }
    ],
    "total": 25,
    "page": 1,
    "limit": 10,
    "facets": {
      "categories": [
        {"value": "meeting", "count": 15},
        {"value": "lecture", "count": 10}
      ],
      "tags": [...],
      "dateRanges": [...]
    }
  }
}
```

## WebSocket Events

### Connection

Connect to WebSocket for real-time features:
```javascript
const socket = io('ws://localhost:3001', {
  auth: {
    token: 'your_jwt_token'
  }
});
```

### Events

#### Transcription Updates
```javascript
// Listen for real-time transcription
socket.on('transcription_update', (data) => {
  console.log('Live transcription:', data.text);
  console.log('Is final:', data.isFinal);
});

// Start transcription session
socket.emit('start_transcription', {
  sessionId: 'session_123',
  settings: {
    language: 'en',
    speakerIdentification: true
  }
});
```

#### Processing Updates
```javascript
// Listen for processing progress
socket.on('processing_update', (data) => {
  console.log('Stage:', data.stage);
  console.log('Progress:', data.progress);
});
```

## Error Handling

All API endpoints return errors in a consistent format:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Common Error Codes

- `AUTHENTICATION_ERROR` - Invalid or missing authentication
- `AUTHORIZATION_ERROR` - Insufficient permissions
- `VALIDATION_ERROR` - Invalid input data
- `NOT_FOUND` - Resource not found
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `INTERNAL_ERROR` - Server error
- `TRANSCRIPTION_FAILED` - Audio processing error
- `AI_SERVICE_UNAVAILABLE` - AI service temporarily unavailable

## Rate Limiting

- **General API**: 100 requests per 15 minutes per IP
- **Audio Upload**: 10 uploads per hour per user
- **Transcription**: 5 concurrent transcriptions per user
- **Search**: 60 requests per minute per user

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time (Unix timestamp)
