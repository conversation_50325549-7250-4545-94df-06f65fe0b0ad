import { Router } from 'express';
import searchController from '../controllers/searchController';
import { authMiddleware } from '../middleware/auth';
import { rateLimiterMiddleware } from '../middleware/rateLimiter';
import { searchValidations } from '../middleware/validation';

const router = Router();

// Apply authentication to all routes
router.use(authMiddleware.requireAuth);

// Apply search-specific rate limiting
router.use(rateLimiterMiddleware.search);

/**
 * @route   GET /api/search
 * @desc    Search across notes and transcriptions
 * @access  Private
 */
router.get(
  '/',
  searchValidations.search,
  searchController.search
);

/**
 * @route   GET /api/search/suggestions
 * @desc    Get search suggestions
 * @access  Private
 */
router.get(
  '/suggestions',
  searchController.getSuggestions
);

/**
 * @route   GET /api/search/facets
 * @desc    Get search facets for filtering
 * @access  Private
 */
router.get(
  '/facets',
  searchController.getFacets
);

export default router;
