import { Server as SocketIOServer, Socket } from 'socket.io';
import { logger } from '../utils/logger';
import { authService } from '../services/authService';
import { WS_EVENTS } from '../../shared/utils/constants';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: {
    id: string;
    email: string;
    name: string;
  };
}

// Store active sessions
const activeSessions = new Map<string, {
  socketId: string;
  userId: string;
  sessionId: string;
  startTime: Date;
}>();

// Store user connections
const userConnections = new Map<string, Set<string>>();

export const setupWebSocketHandlers = (io: SocketIOServer): void => {
  
  // Authentication middleware
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization;
      
      if (!token) {
        return next(new Error('Authentication error: No token provided'));
      }

      // Remove 'Bearer ' prefix if present
      const cleanToken = token.replace('Bearer ', '');
      
      // Verify token
      const decoded = await authService.verifyAccessToken(cleanToken);
      if (!decoded) {
        return next(new Error('Authentication error: Invalid token'));
      }

      // Attach user info to socket
      socket.userId = decoded.userId;
      socket.user = {
        id: decoded.userId,
        email: decoded.email || '',
        name: decoded.name || ''
      };

      next();
    } catch (error) {
      logger.error('WebSocket authentication error', {
        error: error.message,
        socketId: socket.id
      });
      next(new Error('Authentication error: Token verification failed'));
    }
  });

  // Connection handler
  io.on('connection', (socket: AuthenticatedSocket) => {
    const userId = socket.userId!;
    
    logger.info('WebSocket client connected', {
      socketId: socket.id,
      userId,
      userAgent: socket.handshake.headers['user-agent']
    });

    // Track user connections
    if (!userConnections.has(userId)) {
      userConnections.set(userId, new Set());
    }
    userConnections.get(userId)!.add(socket.id);

    // Join user-specific room
    socket.join(`user:${userId}`);

    // Send connection confirmation
    socket.emit(WS_EVENTS.AUTHENTICATED, {
      message: 'Successfully authenticated',
      userId,
      timestamp: new Date().toISOString()
    });

    // Handle transcription session start
    socket.on(WS_EVENTS.START_TRANSCRIPTION, async (data) => {
      try {
        const { sessionId, settings } = data;
        
        logger.info('Transcription session started', {
          sessionId,
          userId,
          socketId: socket.id,
          settings
        });

        // Store session info
        activeSessions.set(sessionId, {
          socketId: socket.id,
          userId,
          sessionId,
          startTime: new Date()
        });

        // Join session room
        socket.join(`session:${sessionId}`);

        // Acknowledge session start
        socket.emit(WS_EVENTS.TRANSCRIPTION_UPDATE, {
          sessionId,
          type: 'session_started',
          message: 'Transcription session started',
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error('Error starting transcription session', {
          error: error.message,
          userId,
          socketId: socket.id
        });
        
        socket.emit(WS_EVENTS.TRANSCRIPTION_ERROR, {
          error: 'Failed to start transcription session',
          timestamp: new Date().toISOString()
        });
      }
    });

    // Handle transcription session stop
    socket.on(WS_EVENTS.STOP_TRANSCRIPTION, async (data) => {
      try {
        const { sessionId } = data;
        
        logger.info('Transcription session stopped', {
          sessionId,
          userId,
          socketId: socket.id
        });

        // Remove session
        activeSessions.delete(sessionId);
        socket.leave(`session:${sessionId}`);

        // Acknowledge session stop
        socket.emit(WS_EVENTS.TRANSCRIPTION_COMPLETE, {
          sessionId,
          message: 'Transcription session completed',
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error('Error stopping transcription session', {
          error: error.message,
          userId,
          socketId: socket.id
        });
      }
    });

    // Handle audio chunk for real-time transcription
    socket.on('audio_chunk', async (data) => {
      try {
        const { sessionId, audioData, chunkIndex } = data;
        
        // Verify session exists
        const session = activeSessions.get(sessionId);
        if (!session || session.socketId !== socket.id) {
          socket.emit(WS_EVENTS.TRANSCRIPTION_ERROR, {
            error: 'Invalid session',
            timestamp: new Date().toISOString()
          });
          return;
        }

        // TODO: Process audio chunk with AI service
        // This would typically queue the audio for processing
        logger.debug('Audio chunk received', {
          sessionId,
          chunkIndex,
          dataSize: audioData?.length || 0,
          userId
        });

        // Simulate transcription result (replace with actual AI processing)
        setTimeout(() => {
          socket.emit(WS_EVENTS.TRANSCRIPTION_UPDATE, {
            sessionId,
            text: `Transcribed chunk ${chunkIndex}...`,
            isFinal: false,
            confidence: 0.85,
            timestamp: new Date().toISOString()
          });
        }, 100);

      } catch (error) {
        logger.error('Error processing audio chunk', {
          error: error.message,
          userId,
          socketId: socket.id
        });
      }
    });

    // Handle note collaboration
    socket.on(WS_EVENTS.JOIN_NOTE, async (data) => {
      try {
        const { noteId } = data;
        
        // TODO: Verify user has access to note
        socket.join(`note:${noteId}`);
        
        // Notify other users in the note
        socket.to(`note:${noteId}`).emit(WS_EVENTS.USER_TYPING, {
          noteId,
          userId,
          userName: socket.user?.name,
          action: 'joined',
          timestamp: new Date().toISOString()
        });

        logger.info('User joined note collaboration', {
          noteId,
          userId,
          socketId: socket.id
        });

      } catch (error) {
        logger.error('Error joining note', {
          error: error.message,
          userId,
          socketId: socket.id
        });
      }
    });

    socket.on(WS_EVENTS.LEAVE_NOTE, async (data) => {
      try {
        const { noteId } = data;
        
        socket.leave(`note:${noteId}`);
        
        // Notify other users
        socket.to(`note:${noteId}`).emit(WS_EVENTS.USER_STOPPED_TYPING, {
          noteId,
          userId,
          userName: socket.user?.name,
          action: 'left',
          timestamp: new Date().toISOString()
        });

        logger.info('User left note collaboration', {
          noteId,
          userId,
          socketId: socket.id
        });

      } catch (error) {
        logger.error('Error leaving note', {
          error: error.message,
          userId,
          socketId: socket.id
        });
      }
    });

    // Handle note updates
    socket.on(WS_EVENTS.NOTE_UPDATE, async (data) => {
      try {
        const { noteId, content, title } = data;
        
        // Broadcast update to other users in the note
        socket.to(`note:${noteId}`).emit(WS_EVENTS.NOTE_UPDATE, {
          noteId,
          content,
          title,
          userId,
          userName: socket.user?.name,
          timestamp: new Date().toISOString()
        });

        logger.debug('Note update broadcasted', {
          noteId,
          userId,
          socketId: socket.id
        });

      } catch (error) {
        logger.error('Error broadcasting note update', {
          error: error.message,
          userId,
          socketId: socket.id
        });
      }
    });

    // Handle typing indicators
    socket.on(WS_EVENTS.USER_TYPING, async (data) => {
      try {
        const { noteId } = data;
        
        socket.to(`note:${noteId}`).emit(WS_EVENTS.USER_TYPING, {
          noteId,
          userId,
          userName: socket.user?.name,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error('Error handling typing indicator', {
          error: error.message,
          userId,
          socketId: socket.id
        });
      }
    });

    socket.on(WS_EVENTS.USER_STOPPED_TYPING, async (data) => {
      try {
        const { noteId } = data;
        
        socket.to(`note:${noteId}`).emit(WS_EVENTS.USER_STOPPED_TYPING, {
          noteId,
          userId,
          userName: socket.user?.name,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error('Error handling stop typing indicator', {
          error: error.message,
          userId,
          socketId: socket.id
        });
      }
    });

    // Handle heartbeat
    socket.on('heartbeat', () => {
      socket.emit('heartbeat_response', {
        timestamp: new Date().toISOString()
      });
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info('WebSocket client disconnected', {
        socketId: socket.id,
        userId,
        reason
      });

      // Clean up user connections
      const userSockets = userConnections.get(userId);
      if (userSockets) {
        userSockets.delete(socket.id);
        if (userSockets.size === 0) {
          userConnections.delete(userId);
        }
      }

      // Clean up active sessions
      for (const [sessionId, session] of activeSessions.entries()) {
        if (session.socketId === socket.id) {
          activeSessions.delete(sessionId);
          logger.info('Cleaned up abandoned transcription session', {
            sessionId,
            userId
          });
        }
      }
    });

    // Handle errors
    socket.on('error', (error) => {
      logger.error('WebSocket error', {
        error: error.message,
        socketId: socket.id,
        userId
      });
    });
  });

  // Periodic cleanup of stale sessions
  setInterval(() => {
    const now = new Date();
    const staleThreshold = 30 * 60 * 1000; // 30 minutes

    for (const [sessionId, session] of activeSessions.entries()) {
      if (now.getTime() - session.startTime.getTime() > staleThreshold) {
        activeSessions.delete(sessionId);
        logger.info('Cleaned up stale transcription session', {
          sessionId,
          userId: session.userId,
          age: now.getTime() - session.startTime.getTime()
        });
      }
    }
  }, 5 * 60 * 1000); // Run every 5 minutes

  logger.info('WebSocket handlers initialized');
};

// Helper functions for external use
export const broadcastToUser = (io: SocketIOServer, userId: string, event: string, data: any): void => {
  io.to(`user:${userId}`).emit(event, data);
};

export const broadcastToNote = (io: SocketIOServer, noteId: string, event: string, data: any): void => {
  io.to(`note:${noteId}`).emit(event, data);
};

export const getActiveSessionsCount = (): number => {
  return activeSessions.size;
};

export const getUserConnectionsCount = (): number => {
  return userConnections.size;
};
