import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  DocumentTextIcon,
  SpeakerWaveIcon,
  ClockIcon,
  StarIcon,
  PlusIcon,
  ArrowTrendingUpIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

import { useAuth } from '../hooks/useAuth';
import { useNotes } from '../hooks/useNotes';
import NoteCard from '../components/Notes/NoteCard';
import AudioRecorder from '../components/AudioRecorder/AudioRecorder';

interface DashboardStats {
  totalNotes: number;
  totalAudioFiles: number;
  recentNotes: number;
  favoriteNotes: number;
  categoryBreakdown: Array<{ category: string; count: number }>;
}

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const { notes, loading, refreshNotes } = useNotes();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [showRecorder, setShowRecorder] = useState(false);

  // Calculate stats from notes
  useEffect(() => {
    if (notes.length > 0) {
      const now = new Date();
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      
      const recentNotes = notes.filter(note => 
        new Date(note.createdAt) >= sevenDaysAgo
      ).length;
      
      const favoriteNotes = notes.filter(note => note.isFavorite).length;
      const audioFiles = notes.filter(note => note.audioFile).length;
      
      // Category breakdown
      const categoryMap = new Map<string, number>();
      notes.forEach(note => {
        const count = categoryMap.get(note.category) || 0;
        categoryMap.set(note.category, count + 1);
      });
      
      const categoryBreakdown = Array.from(categoryMap.entries()).map(([category, count]) => ({
        category,
        count
      }));

      setStats({
        totalNotes: notes.length,
        totalAudioFiles: audioFiles,
        recentNotes,
        favoriteNotes,
        categoryBreakdown
      });
    }
  }, [notes]);

  const recentNotes = notes
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    .slice(0, 6);

  const favoriteNotes = notes
    .filter(note => note.isFavorite)
    .slice(0, 3);

  const handleRecordingComplete = async (audioBlob: Blob, duration: number) => {
    // Handle the completed recording
    console.log('Recording completed:', { audioBlob, duration });
    setShowRecorder(false);
    // TODO: Upload audio and create note
  };

  const handleTranscriptionUpdate = (text: string, isFinal: boolean) => {
    // Handle live transcription updates
    console.log('Transcription update:', { text, isFinal });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {user?.name}!
          </h1>
          <p className="text-gray-600 mt-2">
            Here's what's happening with your notes today.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => setShowRecorder(true)}
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
            >
              <SpeakerWaveIcon className="h-5 w-5 mr-2" />
              Start Recording
            </button>
            
            <button
              onClick={() => window.location.href = '/notes/new'}
              className="inline-flex items-center px-6 py-3 bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors shadow-sm"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              New Note
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg p-6 shadow-sm border border-gray-200"
            >
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <DocumentTextIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Notes</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalNotes}</p>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-lg p-6 shadow-sm border border-gray-200"
            >
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <SpeakerWaveIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Audio Files</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalAudioFiles}</p>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-lg p-6 shadow-sm border border-gray-200"
            >
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <ClockIcon className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Recent Notes</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.recentNotes}</p>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-lg p-6 shadow-sm border border-gray-200"
            >
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <StarIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Favorites</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.favoriteNotes}</p>
                </div>
              </div>
            </motion.div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Notes */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900">Recent Notes</h2>
                  <a
                    href="/notes"
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    View all
                  </a>
                </div>
              </div>
              
              <div className="p-6">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                ) : recentNotes.length > 0 ? (
                  <div className="space-y-4">
                    {recentNotes.map(note => (
                      <NoteCard
                        key={note.id}
                        note={note}
                        viewMode="list"
                        onClick={(note) => window.location.href = `/notes/${note.id}`}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No notes yet</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Get started by creating your first note.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Favorites */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Favorites</h2>
              </div>
              
              <div className="p-6">
                {favoriteNotes.length > 0 ? (
                  <div className="space-y-3">
                    {favoriteNotes.map(note => (
                      <div
                        key={note.id}
                        className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer"
                        onClick={() => window.location.href = `/notes/${note.id}`}
                      >
                        <StarIcon className="h-5 w-5 text-yellow-500 mt-0.5" />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {note.title}
                          </p>
                          <p className="text-xs text-gray-500">
                            {new Date(note.updatedAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <StarIcon className="mx-auto h-8 w-8 text-gray-400" />
                    <p className="mt-2 text-sm text-gray-500">No favorites yet</p>
                  </div>
                )}
              </div>
            </div>

            {/* Category Breakdown */}
            {stats && stats.categoryBreakdown.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Categories</h2>
                </div>
                
                <div className="p-6">
                  <div className="space-y-3">
                    {stats.categoryBreakdown.map(({ category, count }) => (
                      <div key={category} className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700 capitalize">
                          {category.toLowerCase()}
                        </span>
                        <span className="text-sm text-gray-500">{count}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Quick Stats */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
              <div className="flex items-center">
                <ArrowTrendingUpIcon className="h-8 w-8" />
                <div className="ml-4">
                  <p className="text-sm opacity-90">This Week</p>
                  <p className="text-2xl font-bold">{stats?.recentNotes || 0}</p>
                  <p className="text-sm opacity-90">New Notes</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Audio Recorder Modal */}
        {showRecorder && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Record Audio Note</h2>
                <button
                  onClick={() => setShowRecorder(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <AudioRecorder
                onRecordingComplete={handleRecordingComplete}
                onTranscriptionUpdate={handleTranscriptionUpdate}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
