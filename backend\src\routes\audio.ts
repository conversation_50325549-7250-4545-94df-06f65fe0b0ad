import { Router } from 'express';
import audioController, { audioUpload } from '../controllers/audioController';
import { authMiddleware } from '../middleware/auth';
import { rateLimiterMiddleware } from '../middleware/rateLimiter';
import { audioValidations } from '../middleware/validation';

const router = Router();

// Apply authentication to all routes
router.use(authMiddleware.requireAuth);

/**
 * @route   POST /api/audio/upload
 * @desc    Upload audio file
 * @access  Private
 */
router.post(
  '/upload',
  rateLimiterMiddleware.audioUpload,
  audioUpload.single('audioFile'),
  audioValidations.upload,
  audioController.uploadAudio
);

/**
 * @route   GET /api/audio
 * @desc    Get all audio files for the authenticated user
 * @access  Private
 */
router.get(
  '/',
  rateLimiterMiddleware.general,
  audioController.getAudioFiles
);

/**
 * @route   GET /api/audio/:id
 * @desc    Get audio file by ID
 * @access  Private
 */
router.get(
  '/:id',
  rateLimiterMiddleware.general,
  audioController.getAudio
);

/**
 * @route   GET /api/audio/:id/stream
 * @desc    Stream audio file
 * @access  Private
 */
router.get(
  '/:id/stream',
  rateLimiterMiddleware.general,
  audioController.streamAudio
);

/**
 * @route   POST /api/audio/:id/transcribe
 * @desc    Trigger transcription for audio file
 * @access  Private
 */
router.post(
  '/:id/transcribe',
  rateLimiterMiddleware.aiProcessing,
  audioValidations.transcribe,
  audioController.transcribeAudio
);

/**
 * @route   DELETE /api/audio/:id
 * @desc    Delete audio file
 * @access  Private
 */
router.delete(
  '/:id',
  audioValidations.delete,
  audioController.deleteAudio
);

export default router;
