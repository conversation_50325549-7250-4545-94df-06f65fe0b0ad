{"name": "quickcapt-frontend", "version": "1.0.0", "description": "QuickCapt Frontend - React PWA for AI Note Taking", "private": true, "dependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "typescript": "^5.3.0", "web-vitals": "^3.5.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "date-fns": "^2.30.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "wavesurfer.js": "^7.6.0", "recordrtc": "^5.6.2", "react-dropzone": "^14.2.3", "react-virtualized": "^9.22.5", "fuse.js": "^7.0.0", "react-helmet-async": "^2.0.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/react-virtualized": "^9.21.29", "eslint": "^8.55.0", "eslint-config-react-app": "^7.0.1", "prettier": "^3.1.0", "workbox-cli": "^7.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && npm run build:sw", "build:sw": "workbox generateSW workbox-config.js", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}", "analyze": "npm run build && npx source-map-explorer 'build/static/js/*.js'"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}