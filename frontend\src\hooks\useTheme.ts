import { useState, useEffect, useContext, createContext, ReactNode } from 'react';
import { STORAGE_KEYS, THEMES } from '../utils/constants';

type Theme = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  actualTheme: 'light' | 'dark';
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  isDark: boolean;
  isLight: boolean;
  isSystem: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: Theme;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  defaultTheme = 'system' 
}) => {
  const [theme, setThemeState] = useState<Theme>(() => {
    // Get theme from localStorage or use default
    const savedTheme = localStorage.getItem(STORAGE_KEYS.THEME) as Theme;
    return savedTheme || defaultTheme;
  });

  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light');

  // Get system theme preference
  const getSystemTheme = (): 'light' | 'dark' => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light';
  };

  // Calculate actual theme based on current theme setting
  const calculateActualTheme = (currentTheme: Theme): 'light' | 'dark' => {
    if (currentTheme === 'system') {
      return getSystemTheme();
    }
    return currentTheme;
  };

  // Update actual theme when theme changes
  useEffect(() => {
    const newActualTheme = calculateActualTheme(theme);
    setActualTheme(newActualTheme);
  }, [theme]);

  // Listen for system theme changes
  useEffect(() => {
    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = (e: MediaQueryListEvent) => {
        setActualTheme(e.matches ? 'dark' : 'light');
      };

      mediaQuery.addEventListener('change', handleChange);
      
      return () => {
        mediaQuery.removeEventListener('change', handleChange);
      };
    }
  }, [theme]);

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;
    
    // Remove existing theme classes
    root.classList.remove('light', 'dark');
    
    // Add current theme class
    root.classList.add(actualTheme);
    
    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute(
        'content', 
        actualTheme === 'dark' ? '#1f2937' : '#ffffff'
      );
    }

    // Update CSS custom properties for theme
    const themeColors = {
      light: {
        '--color-background': '#ffffff',
        '--color-surface': '#f8fafc',
        '--color-text': '#1e293b',
        '--color-text-secondary': '#64748b',
        '--color-border': '#e2e8f0'
      },
      dark: {
        '--color-background': '#0f172a',
        '--color-surface': '#1e293b',
        '--color-text': '#f1f5f9',
        '--color-text-secondary': '#94a3b8',
        '--color-border': '#334155'
      }
    };

    const colors = themeColors[actualTheme];
    Object.entries(colors).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });
  }, [actualTheme]);

  // Save theme to localStorage
  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.THEME, theme);
  }, [theme]);

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
  };

  const toggleTheme = () => {
    const themes: Theme[] = ['light', 'dark', 'system'];
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  // Computed properties
  const isDark = actualTheme === 'dark';
  const isLight = actualTheme === 'light';
  const isSystem = theme === 'system';

  const value: ThemeContextType = {
    theme,
    actualTheme,
    setTheme,
    toggleTheme,
    isDark,
    isLight,
    isSystem
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Hook for theme-aware styling
export const useThemeStyles = () => {
  const { isDark, isLight, actualTheme } = useTheme();

  const getThemeClass = (lightClass: string, darkClass: string): string => {
    return isDark ? darkClass : lightClass;
  };

  const getThemeValue = <T>(lightValue: T, darkValue: T): T => {
    return isDark ? darkValue : lightValue;
  };

  const getThemeColors = () => ({
    background: getThemeValue('#ffffff', '#0f172a'),
    surface: getThemeValue('#f8fafc', '#1e293b'),
    text: getThemeValue('#1e293b', '#f1f5f9'),
    textSecondary: getThemeValue('#64748b', '#94a3b8'),
    border: getThemeValue('#e2e8f0', '#334155'),
    primary: '#3b82f6',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444'
  });

  return {
    isDark,
    isLight,
    actualTheme,
    getThemeClass,
    getThemeValue,
    getThemeColors
  };
};

// Hook for theme-aware animations
export const useThemeTransition = () => {
  const { actualTheme } = useTheme();
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    setIsTransitioning(true);
    const timer = setTimeout(() => {
      setIsTransitioning(false);
    }, 200);

    return () => clearTimeout(timer);
  }, [actualTheme]);

  return { isTransitioning };
};

// Hook for system theme detection
export const useSystemTheme = () => {
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>(() => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light';
  });

  useEffect(() => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = (e: MediaQueryListEvent) => {
        setSystemTheme(e.matches ? 'dark' : 'light');
      };

      mediaQuery.addEventListener('change', handleChange);
      
      return () => {
        mediaQuery.removeEventListener('change', handleChange);
      };
    }
  }, []);

  return systemTheme;
};

// Hook for theme persistence across sessions
export const useThemePersistence = () => {
  const { theme, setTheme } = useTheme();

  const saveThemePreference = (newTheme: Theme) => {
    setTheme(newTheme);
    
    // Also save to user preferences if authenticated
    // This would typically sync with the backend
    try {
      const userPreferences = JSON.parse(
        localStorage.getItem(STORAGE_KEYS.USER_PREFERENCES) || '{}'
      );
      userPreferences.theme = newTheme;
      localStorage.setItem(
        STORAGE_KEYS.USER_PREFERENCES, 
        JSON.stringify(userPreferences)
      );
    } catch (error) {
      console.warn('Failed to save theme to user preferences:', error);
    }
  };

  const loadThemePreference = (): Theme => {
    try {
      // First try to get from user preferences
      const userPreferences = JSON.parse(
        localStorage.getItem(STORAGE_KEYS.USER_PREFERENCES) || '{}'
      );
      if (userPreferences.theme) {
        return userPreferences.theme;
      }
    } catch (error) {
      console.warn('Failed to load theme from user preferences:', error);
    }

    // Fallback to theme storage
    return (localStorage.getItem(STORAGE_KEYS.THEME) as Theme) || 'system';
  };

  return {
    theme,
    saveThemePreference,
    loadThemePreference
  };
};

export default useTheme;
