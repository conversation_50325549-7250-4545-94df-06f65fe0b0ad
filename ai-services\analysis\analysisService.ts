import OpenAI from 'openai';
import { logger } from '../utils/logger';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export interface AnalysisResult {
  summary: string;
  keyPoints: string[];
  sentiment: 'positive' | 'negative' | 'neutral';
  sentimentScore: number;
  topics: string[];
  actionItems: string[];
  questions: string[];
  entities: {
    people: string[];
    organizations: string[];
    locations: string[];
    dates: string[];
  };
  confidence: number;
  processingTime: number;
}

export interface SpeakerDiarizationResult {
  speakers: Array<{
    id: string;
    name?: string;
    segments: Array<{
      start: number;
      end: number;
      text: string;
      confidence: number;
    }>;
  }>;
  totalSpeakers: number;
  confidence: number;
}

export class AnalysisService {
  
  /**
   * Analyze transcribed text for insights and key information
   */
  async analyzeText(text: string, context?: {
    category?: string;
    language?: string;
    userId?: string;
  }): Promise<AnalysisResult> {
    const startTime = Date.now();
    
    try {
      logger.info('Starting text analysis', {
        textLength: text.length,
        category: context?.category,
        language: context?.language,
        userId: context?.userId
      });

      const prompt = this.buildAnalysisPrompt(text, context);
      
      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert text analyst. Analyze the provided text and return a comprehensive analysis in the specified JSON format. Be thorough but concise.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000,
        response_format: { type: 'json_object' }
      });

      const analysisText = response.choices[0]?.message?.content;
      if (!analysisText) {
        throw new Error('No analysis response received from OpenAI');
      }

      const analysis = JSON.parse(analysisText);
      const processingTime = Date.now() - startTime;

      const result: AnalysisResult = {
        summary: analysis.summary || '',
        keyPoints: analysis.keyPoints || [],
        sentiment: analysis.sentiment || 'neutral',
        sentimentScore: analysis.sentimentScore || 0,
        topics: analysis.topics || [],
        actionItems: analysis.actionItems || [],
        questions: analysis.questions || [],
        entities: {
          people: analysis.entities?.people || [],
          organizations: analysis.entities?.organizations || [],
          locations: analysis.entities?.locations || [],
          dates: analysis.entities?.dates || []
        },
        confidence: analysis.confidence || 0.8,
        processingTime
      };

      logger.info('Text analysis completed', {
        processingTime,
        summaryLength: result.summary.length,
        keyPointsCount: result.keyPoints.length,
        sentiment: result.sentiment,
        topicsCount: result.topics.length,
        userId: context?.userId
      });

      return result;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error('Text analysis failed', {
        error: error.message,
        processingTime,
        textLength: text.length,
        userId: context?.userId
      });
      throw error;
    }
  }

  /**
   * Perform speaker diarization on transcribed text
   */
  async performSpeakerDiarization(
    text: string,
    audioMetadata?: {
      duration: number;
      channels: number;
      sampleRate: number;
    }
  ): Promise<SpeakerDiarizationResult> {
    try {
      logger.info('Starting speaker diarization', {
        textLength: text.length,
        audioMetadata
      });

      const prompt = `
        Analyze the following transcribed text and identify different speakers. 
        Return the result in JSON format with the following structure:
        {
          "speakers": [
            {
              "id": "speaker_1",
              "name": "Speaker 1 (or actual name if identifiable)",
              "segments": [
                {
                  "start": 0,
                  "end": 30,
                  "text": "segment text",
                  "confidence": 0.95
                }
              ]
            }
          ],
          "totalSpeakers": 2,
          "confidence": 0.85
        }

        Text to analyze:
        ${text}
      `;

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert in speaker diarization. Analyze the text and identify different speakers based on context, speaking patterns, and content changes.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 1500,
        response_format: { type: 'json_object' }
      });

      const diarizationText = response.choices[0]?.message?.content;
      if (!diarizationText) {
        throw new Error('No diarization response received from OpenAI');
      }

      const result = JSON.parse(diarizationText) as SpeakerDiarizationResult;

      logger.info('Speaker diarization completed', {
        totalSpeakers: result.totalSpeakers,
        confidence: result.confidence,
        segmentsCount: result.speakers.reduce((sum, speaker) => sum + speaker.segments.length, 0)
      });

      return result;
    } catch (error) {
      logger.error('Speaker diarization failed', {
        error: error.message,
        textLength: text.length
      });
      throw error;
    }
  }

  /**
   * Generate meeting minutes from transcribed text
   */
  async generateMeetingMinutes(text: string, meetingContext?: {
    title?: string;
    attendees?: string[];
    date?: string;
    agenda?: string[];
  }): Promise<{
    title: string;
    date: string;
    attendees: string[];
    agenda: string[];
    summary: string;
    keyDecisions: string[];
    actionItems: Array<{
      task: string;
      assignee?: string;
      dueDate?: string;
      priority: 'high' | 'medium' | 'low';
    }>;
    nextSteps: string[];
    followUpRequired: boolean;
  }> {
    try {
      logger.info('Generating meeting minutes', {
        textLength: text.length,
        hasContext: !!meetingContext
      });

      const contextInfo = meetingContext ? `
        Meeting Context:
        - Title: ${meetingContext.title || 'Not specified'}
        - Date: ${meetingContext.date || 'Not specified'}
        - Attendees: ${meetingContext.attendees?.join(', ') || 'Not specified'}
        - Agenda: ${meetingContext.agenda?.join(', ') || 'Not specified'}
      ` : '';

      const prompt = `
        Generate comprehensive meeting minutes from the following transcribed text.
        ${contextInfo}

        Return the result in JSON format with the following structure:
        {
          "title": "Meeting title",
          "date": "Meeting date",
          "attendees": ["list of attendees"],
          "agenda": ["agenda items"],
          "summary": "Executive summary",
          "keyDecisions": ["important decisions made"],
          "actionItems": [
            {
              "task": "task description",
              "assignee": "person responsible",
              "dueDate": "due date if mentioned",
              "priority": "high/medium/low"
            }
          ],
          "nextSteps": ["next steps and follow-ups"],
          "followUpRequired": true/false
        }

        Transcribed text:
        ${text}
      `;

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert meeting secretary. Generate professional, comprehensive meeting minutes that capture all important information, decisions, and action items.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2500,
        response_format: { type: 'json_object' }
      });

      const minutesText = response.choices[0]?.message?.content;
      if (!minutesText) {
        throw new Error('No meeting minutes response received from OpenAI');
      }

      const result = JSON.parse(minutesText);

      logger.info('Meeting minutes generated', {
        keyDecisionsCount: result.keyDecisions?.length || 0,
        actionItemsCount: result.actionItems?.length || 0,
        followUpRequired: result.followUpRequired
      });

      return result;
    } catch (error) {
      logger.error('Meeting minutes generation failed', {
        error: error.message,
        textLength: text.length
      });
      throw error;
    }
  }

  /**
   * Extract and categorize questions from text
   */
  async extractQuestions(text: string): Promise<{
    questions: Array<{
      question: string;
      category: 'clarification' | 'action' | 'information' | 'decision' | 'other';
      context: string;
      priority: 'high' | 'medium' | 'low';
      answered: boolean;
      answer?: string;
    }>;
    totalQuestions: number;
    unansweredCount: number;
  }> {
    try {
      const prompt = `
        Extract all questions from the following text and categorize them.
        Return the result in JSON format with the following structure:
        {
          "questions": [
            {
              "question": "the actual question",
              "category": "clarification/action/information/decision/other",
              "context": "surrounding context",
              "priority": "high/medium/low",
              "answered": true/false,
              "answer": "answer if provided in text"
            }
          ],
          "totalQuestions": 5,
          "unansweredCount": 2
        }

        Text to analyze:
        ${text}
      `;

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at identifying and categorizing questions in text. Extract all explicit and implicit questions, determine if they were answered, and categorize them appropriately.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 1500,
        response_format: { type: 'json_object' }
      });

      const questionsText = response.choices[0]?.message?.content;
      if (!questionsText) {
        throw new Error('No questions response received from OpenAI');
      }

      const result = JSON.parse(questionsText);

      logger.info('Questions extracted', {
        totalQuestions: result.totalQuestions,
        unansweredCount: result.unansweredCount
      });

      return result;
    } catch (error) {
      logger.error('Question extraction failed', {
        error: error.message,
        textLength: text.length
      });
      throw error;
    }
  }

  /**
   * Build analysis prompt based on context
   */
  private buildAnalysisPrompt(text: string, context?: {
    category?: string;
    language?: string;
  }): string {
    const categoryContext = context?.category ? `
      This text is from a ${context.category.toLowerCase()} context. 
      Please tailor your analysis accordingly.
    ` : '';

    const languageContext = context?.language && context.language !== 'en' ? `
      The original language is ${context.language}. 
      Please provide analysis in English but be aware of cultural context.
    ` : '';

    return `
      Analyze the following text and provide a comprehensive analysis in JSON format:
      {
        "summary": "A concise summary of the main content (2-3 sentences)",
        "keyPoints": ["array of key points and important information"],
        "sentiment": "positive/negative/neutral",
        "sentimentScore": 0.0-1.0,
        "topics": ["main topics and themes discussed"],
        "actionItems": ["specific action items or tasks mentioned"],
        "questions": ["important questions raised or that need answers"],
        "entities": {
          "people": ["names of people mentioned"],
          "organizations": ["companies, organizations mentioned"],
          "locations": ["places, locations mentioned"],
          "dates": ["important dates mentioned"]
        },
        "confidence": 0.0-1.0
      }

      ${categoryContext}
      ${languageContext}

      Text to analyze:
      ${text}
    `;
  }
}

export default new AnalysisService();
