import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { STORAGE_KEYS } from '../utils/constants';

// Create axios instance
export const api: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors and token refresh
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
        if (refreshToken) {
          const response = await axios.post(
            `${process.env.REACT_APP_API_URL || 'http://localhost:3001/api'}/auth/refresh`,
            { refreshToken }
          );

          if (response.data.success) {
            const { token, refreshToken: newRefreshToken } = response.data.data;
            
            // Update stored tokens
            localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
            localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, newRefreshToken);
            
            // Update the authorization header
            api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
            originalRequest.headers.Authorization = `Bearer ${token}`;
            
            // Retry the original request
            return api(originalRequest);
          }
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
        localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
        localStorage.removeItem(STORAGE_KEYS.USER_DATA);
        
        // Only redirect if not already on login page
        if (!window.location.pathname.includes('/login')) {
          window.location.href = '/login';
        }
        
        return Promise.reject(refreshError);
      }
    }

    // Handle network errors
    if (!error.response) {
      error.message = 'Network error. Please check your connection.';
    }

    return Promise.reject(error);
  }
);

// API service methods
export const apiService = {
  // Auth endpoints
  auth: {
    login: (credentials: { email: string; password: string; rememberMe?: boolean }) =>
      api.post('/auth/login', credentials),
    
    register: (userData: { name: string; email: string; password: string; confirmPassword: string }) =>
      api.post('/auth/register', userData),
    
    logout: () => api.post('/auth/logout'),
    
    refreshToken: (refreshToken: string) =>
      api.post('/auth/refresh', { refreshToken }),
    
    getProfile: () => api.get('/auth/profile'),
    
    updateProfile: (data: { name?: string; avatar?: string }) =>
      api.put('/auth/profile', data),
    
    changePassword: (data: { currentPassword: string; password: string; confirmPassword: string }) =>
      api.put('/auth/change-password', data),
    
    forgotPassword: (email: string) =>
      api.post('/auth/forgot-password', { email }),
    
    resetPassword: (data: { token: string; password: string; confirmPassword: string }) =>
      api.post('/auth/reset-password', data),
  },

  // Notes endpoints
  notes: {
    getAll: (params?: {
      page?: number;
      limit?: number;
      q?: string;
      category?: string;
      dateFrom?: string;
      dateTo?: string;
      sortBy?: string;
      sortOrder?: string;
      isArchived?: boolean;
      isFavorite?: boolean;
    }) => api.get('/notes', { params }),
    
    getById: (id: string) => api.get(`/notes/${id}`),
    
    create: (data: {
      title: string;
      content?: string;
      category?: string;
      tags?: string[];
      audioFileId?: string;
      isPublic?: boolean;
    }) => api.post('/notes', data),
    
    update: (id: string, data: {
      title?: string;
      content?: string;
      category?: string;
      tags?: string[];
      isPublic?: boolean;
      isFavorite?: boolean;
    }) => api.put(`/notes/${id}`, data),
    
    delete: (id: string) => api.delete(`/notes/${id}`),
    
    archive: (id: string, isArchived: boolean) =>
      api.patch(`/notes/${id}/archive`, { isArchived }),
    
    toggleFavorite: (id: string) => api.patch(`/notes/${id}/favorite`),
    
    getStats: () => api.get('/notes/stats'),
  },

  // Audio endpoints
  audio: {
    getAll: (params?: {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: string;
    }) => api.get('/audio', { params }),
    
    getById: (id: string) => api.get(`/audio/${id}`),
    
    upload: (formData: FormData) =>
      api.post('/audio/upload', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
        timeout: 300000, // 5 minutes for large files
      }),
    
    delete: (id: string) => api.delete(`/audio/${id}`),
    
    transcribe: (id: string, options?: {
      language?: string;
      speakerIdentification?: boolean;
      timestamps?: boolean;
    }) => api.post(`/audio/${id}/transcribe`, options),
    
    getStreamUrl: (id: string) => `${api.defaults.baseURL}/audio/${id}/stream`,
  },

  // Search endpoints
  search: {
    search: (params: {
      q: string;
      type?: 'all' | 'notes' | 'transcriptions';
      page?: number;
      limit?: number;
      filters?: string;
    }) => api.get('/search', { params }),
    
    getSuggestions: (query: string, limit?: number) =>
      api.get('/search/suggestions', { params: { q: query, limit } }),
    
    getFacets: () => api.get('/search/facets'),
  },
};

// Utility functions
export const createFormData = (data: Record<string, any>): FormData => {
  const formData = new FormData();
  
  Object.keys(data).forEach(key => {
    const value = data[key];
    if (value !== null && value !== undefined) {
      if (value instanceof File || value instanceof Blob) {
        formData.append(key, value);
      } else if (Array.isArray(value)) {
        value.forEach(item => formData.append(key, item));
      } else {
        formData.append(key, String(value));
      }
    }
  });
  
  return formData;
};

export const handleApiError = (error: any): string => {
  if (error.response?.data?.error?.message) {
    return error.response.data.error.message;
  }
  
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error.message) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
};

// Export default api instance
export default api;
