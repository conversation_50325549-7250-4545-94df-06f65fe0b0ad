import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export interface EnvironmentConfig {
  NODE_ENV: string;
  PORT: number;
  DATABASE_URL: string;
  REDIS_URL: string;
  JWT_SECRET: string;
  JWT_EXPIRES_IN: string;
  REFRESH_TOKEN_SECRET: string;
  REFRESH_TOKEN_EXPIRES_IN: string;
  OPENAI_API_KEY: string;
  OPENAI_MODEL: string;
  OPENAI_MAX_TOKENS: number;
  CORS_ORIGIN: string;
  MAX_AUDIO_FILE_SIZE: string;
  SUPPORTED_AUDIO_FORMATS: string;
  STORAGE_TYPE: string;
  STORAGE_PATH: string;
  AWS_ACCESS_KEY_ID?: string;
  AWS_SECRET_ACCESS_KEY?: string;
  AWS_REGION?: string;
  AWS_S3_BUCKET?: string;
  ELASTICSEARCH_URL?: string;
  LOG_LEVEL: string;
}

const requiredEnvVars = [
  'DATABASE_URL',
  'REDIS_URL',
  'JWT_SECRET',
  'OPENAI_API_KEY'
];

// Validate required environment variables
const validateEnvironment = (): void => {
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    console.warn(`Missing required environment variables: ${missing.join(', ')}`);
    console.warn('Please check your .env file configuration');
  }
};

// Validate environment on import
validateEnvironment();

export const environmentConfig: EnvironmentConfig = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  PORT: parseInt(process.env.PORT || '3001', 10),
  DATABASE_URL: process.env.DATABASE_URL || 'postgresql://username:password@localhost:5432/quickcapt',
  REDIS_URL: process.env.REDIS_URL || 'redis://localhost:6379',
  JWT_SECRET: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production',
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',
  REFRESH_TOKEN_SECRET: process.env.REFRESH_TOKEN_SECRET || process.env.JWT_SECRET || 'your-refresh-token-secret',
  REFRESH_TOKEN_EXPIRES_IN: process.env.REFRESH_TOKEN_EXPIRES_IN || '30d',
  OPENAI_API_KEY: process.env.OPENAI_API_KEY || '',
  OPENAI_MODEL: process.env.OPENAI_MODEL || 'gpt-4',
  OPENAI_MAX_TOKENS: parseInt(process.env.OPENAI_MAX_TOKENS || '2000', 10),
  CORS_ORIGIN: process.env.CORS_ORIGIN || 'http://localhost:3000',
  MAX_AUDIO_FILE_SIZE: process.env.MAX_AUDIO_FILE_SIZE || '100MB',
  SUPPORTED_AUDIO_FORMATS: process.env.SUPPORTED_AUDIO_FORMATS || 'mp3,wav,m4a,webm,ogg',
  STORAGE_TYPE: process.env.STORAGE_TYPE || 'local',
  STORAGE_PATH: process.env.STORAGE_PATH || './uploads',
  AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
  AWS_REGION: process.env.AWS_REGION,
  AWS_S3_BUCKET: process.env.AWS_S3_BUCKET,
  ELASTICSEARCH_URL: process.env.ELASTICSEARCH_URL,
  LOG_LEVEL: process.env.LOG_LEVEL || 'info'
};

// Helper functions
export const isDevelopment = (): boolean => environmentConfig.NODE_ENV === 'development';
export const isProduction = (): boolean => environmentConfig.NODE_ENV === 'production';
export const isTest = (): boolean => environmentConfig.NODE_ENV === 'test';

// Audio configuration
export const audioConfig = {
  maxFileSize: environmentConfig.MAX_AUDIO_FILE_SIZE,
  supportedFormats: environmentConfig.SUPPORTED_AUDIO_FORMATS.split(','),
  storageType: environmentConfig.STORAGE_TYPE,
  storagePath: environmentConfig.STORAGE_PATH
};

// OpenAI configuration
export const openaiConfig = {
  apiKey: environmentConfig.OPENAI_API_KEY,
  model: environmentConfig.OPENAI_MODEL,
  maxTokens: environmentConfig.OPENAI_MAX_TOKENS
};

// Database configuration
export const dbConfig = {
  url: environmentConfig.DATABASE_URL,
  ssl: isProduction()
};

// Redis configuration
export const redisConfig = {
  url: environmentConfig.REDIS_URL
};

export default environmentConfig;
