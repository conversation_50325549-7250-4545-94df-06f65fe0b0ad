import { useState, useEffect, useCallback } from 'react';

type SetValue<T> = T | ((val: T) => T);

/**
 * Custom hook for managing localStorage with TypeScript support
 * Provides automatic JSON serialization/deserialization and error handling
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: SetValue<T>) => void, () => void] {
  // Get value from localStorage or return initialValue
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = useCallback(
    (value: SetValue<T>) => {
      try {
        // Allow value to be a function so we have the same API as useState
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        
        // Save state
        setStoredValue(valueToStore);
        
        // Save to localStorage
        if (typeof window !== 'undefined') {
          window.localStorage.setItem(key, JSON.stringify(valueToStore));
        }
      } catch (error) {
        console.warn(`Error setting localStorage key "${key}":`, error);
      }
    },
    [key, storedValue]
  );

  // Remove item from localStorage
  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue);
      if (typeof window !== 'undefined') {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  // Listen for changes to localStorage from other tabs/windows
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          setStoredValue(JSON.parse(e.newValue));
        } catch (error) {
          console.warn(`Error parsing localStorage value for key "${key}":`, error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key]);

  return [storedValue, setValue, removeValue];
}

/**
 * Hook for managing session storage (similar to localStorage but session-scoped)
 */
export function useSessionStorage<T>(
  key: string,
  initialValue: T
): [T, (value: SetValue<T>) => void, () => void] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.sessionStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(`Error reading sessionStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = useCallback(
    (value: SetValue<T>) => {
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        setStoredValue(valueToStore);
        
        if (typeof window !== 'undefined') {
          window.sessionStorage.setItem(key, JSON.stringify(valueToStore));
        }
      } catch (error) {
        console.warn(`Error setting sessionStorage key "${key}":`, error);
      }
    },
    [key, storedValue]
  );

  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue);
      if (typeof window !== 'undefined') {
        window.sessionStorage.removeItem(key);
      }
    } catch (error) {
      console.warn(`Error removing sessionStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  return [storedValue, setValue, removeValue];
}

/**
 * Hook for managing multiple localStorage keys as a single object
 */
export function useLocalStorageObject<T extends Record<string, any>>(
  keyPrefix: string,
  initialValue: T
): [T, (updates: Partial<T>) => void, () => void] {
  const [values, setValues] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    const stored: Partial<T> = {};
    Object.keys(initialValue).forEach(key => {
      try {
        const item = window.localStorage.getItem(`${keyPrefix}_${key}`);
        if (item) {
          stored[key as keyof T] = JSON.parse(item);
        }
      } catch (error) {
        console.warn(`Error reading localStorage key "${keyPrefix}_${key}":`, error);
      }
    });

    return { ...initialValue, ...stored };
  });

  const updateValues = useCallback(
    (updates: Partial<T>) => {
      const newValues = { ...values, ...updates };
      setValues(newValues);

      if (typeof window !== 'undefined') {
        Object.entries(updates).forEach(([key, value]) => {
          try {
            window.localStorage.setItem(`${keyPrefix}_${key}`, JSON.stringify(value));
          } catch (error) {
            console.warn(`Error setting localStorage key "${keyPrefix}_${key}":`, error);
          }
        });
      }
    },
    [keyPrefix, values]
  );

  const clearValues = useCallback(() => {
    setValues(initialValue);
    
    if (typeof window !== 'undefined') {
      Object.keys(initialValue).forEach(key => {
        try {
          window.localStorage.removeItem(`${keyPrefix}_${key}`);
        } catch (error) {
          console.warn(`Error removing localStorage key "${keyPrefix}_${key}":`, error);
        }
      });
    }
  }, [keyPrefix, initialValue]);

  return [values, updateValues, clearValues];
}

/**
 * Hook for managing localStorage with expiration
 */
export function useLocalStorageWithExpiry<T>(
  key: string,
  initialValue: T,
  expiryMinutes: number = 60
): [T, (value: SetValue<T>) => void, () => void, boolean] {
  const [isExpired, setIsExpired] = useState(false);

  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      if (!item) return initialValue;

      const parsed = JSON.parse(item);
      const now = new Date().getTime();
      
      if (parsed.expiry && now > parsed.expiry) {
        // Item has expired
        window.localStorage.removeItem(key);
        setIsExpired(true);
        return initialValue;
      }

      return parsed.value || initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = useCallback(
    (value: SetValue<T>) => {
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        setStoredValue(valueToStore);
        setIsExpired(false);

        if (typeof window !== 'undefined') {
          const now = new Date().getTime();
          const expiry = now + (expiryMinutes * 60 * 1000);
          const item = {
            value: valueToStore,
            expiry
          };
          window.localStorage.setItem(key, JSON.stringify(item));
        }
      } catch (error) {
        console.warn(`Error setting localStorage key "${key}":`, error);
      }
    },
    [key, storedValue, expiryMinutes]
  );

  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue);
      setIsExpired(false);
      if (typeof window !== 'undefined') {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  return [storedValue, setValue, removeValue, isExpired];
}

/**
 * Hook for managing localStorage with size limits
 */
export function useLocalStorageWithLimit<T>(
  key: string,
  initialValue: T,
  maxSizeKB: number = 100
): [T, (value: SetValue<T>) => void, () => void, boolean] {
  const [isOverLimit, setIsOverLimit] = useState(false);

  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = useCallback(
    (value: SetValue<T>) => {
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        const serialized = JSON.stringify(valueToStore);
        const sizeKB = new Blob([serialized]).size / 1024;

        if (sizeKB > maxSizeKB) {
          setIsOverLimit(true);
          console.warn(`localStorage value for key "${key}" exceeds size limit of ${maxSizeKB}KB`);
          return;
        }

        setStoredValue(valueToStore);
        setIsOverLimit(false);

        if (typeof window !== 'undefined') {
          window.localStorage.setItem(key, serialized);
        }
      } catch (error) {
        console.warn(`Error setting localStorage key "${key}":`, error);
      }
    },
    [key, storedValue, maxSizeKB]
  );

  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue);
      setIsOverLimit(false);
      if (typeof window !== 'undefined') {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  return [storedValue, setValue, removeValue, isOverLimit];
}

/**
 * Utility function to get localStorage usage information
 */
export function getLocalStorageInfo() {
  if (typeof window === 'undefined') {
    return { used: 0, available: 0, total: 0 };
  }

  let used = 0;
  for (const key in localStorage) {
    if (localStorage.hasOwnProperty(key)) {
      used += localStorage[key].length + key.length;
    }
  }

  // Estimate total available space (varies by browser, typically 5-10MB)
  const total = 5 * 1024 * 1024; // 5MB estimate
  const available = total - used;

  return {
    used: Math.round(used / 1024), // KB
    available: Math.round(available / 1024), // KB
    total: Math.round(total / 1024), // KB
    usagePercentage: Math.round((used / total) * 100)
  };
}

export default useLocalStorage;
