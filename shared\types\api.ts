// Shared API types between frontend and backend

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  message?: string;
  timestamp: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  stack?: string; // Only in development
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Authentication types
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresAt: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

// User types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
  preferences: UserPreferences;
  subscription: SubscriptionPlan;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  autoSave: boolean;
  notifications: NotificationSettings;
  audioQuality: 'low' | 'medium' | 'high';
  transcriptionLanguage: string;
  speakerIdentification: boolean;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  transcriptionComplete: boolean;
  summaryReady: boolean;
  actionItemReminders: boolean;
}

export interface SubscriptionPlan {
  type: 'free' | 'pro' | 'team' | 'enterprise';
  status: 'active' | 'cancelled' | 'expired';
  expiresAt?: string;
  features: string[];
  limits: {
    monthlyMinutes: number;
    storageGB: number;
    collaborators: number;
  };
}

// Note types
export interface Note {
  id: string;
  title: string;
  content: string;
  summary?: string;
  audioFileId?: string;
  audioUrl?: string;
  audioDuration?: number;
  transcription?: Transcription;
  analysis?: NoteAnalysis;
  tags: string[];
  category: NoteCategory;
  isPublic: boolean;
  collaborators: Collaborator[];
  createdAt: string;
  updatedAt: string;
  userId: string;
  metadata: NoteMetadata;
}

export interface CreateNoteRequest {
  title: string;
  content?: string;
  category?: NoteCategory;
  tags?: string[];
  isPublic?: boolean;
  audioFileId?: string;
}

export interface UpdateNoteRequest {
  title?: string;
  content?: string;
  tags?: string[];
  category?: NoteCategory;
  isPublic?: boolean;
}

export type NoteCategory = 
  | 'meeting'
  | 'lecture'
  | 'interview'
  | 'brainstorm'
  | 'call'
  | 'presentation'
  | 'other';

export interface Collaborator {
  userId: string;
  email: string;
  name: string;
  role: 'viewer' | 'editor' | 'admin';
  addedAt: string;
}

export interface NoteMetadata {
  source: 'web' | 'mobile' | 'api';
  device?: string;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  meetingInfo?: {
    platform: string;
    participants: string[];
    duration: number;
  };
}

// Audio types
export interface AudioFile {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  duration: number;
  sampleRate: number;
  channels: number;
  url: string;
  waveformData?: number[];
  createdAt: string;
}

export interface AudioUploadRequest {
  title?: string;
  category?: NoteCategory;
  language?: string;
  speakerIdentification?: boolean;
}

export interface AudioUploadResponse {
  audioFile: AudioFile;
  processingId: string;
}

// Transcription types
export interface Transcription {
  id: string;
  text: string;
  segments: TranscriptionSegment[];
  speakers: Speaker[];
  language: string;
  confidence: number;
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: string;
}

export interface TranscriptionSegment {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  speakerId?: string;
  confidence: number;
  words: Word[];
}

export interface Word {
  text: string;
  startTime: number;
  endTime: number;
  confidence: number;
}

export interface Speaker {
  id: string;
  name?: string;
  color: string;
  segments: number[];
}

export interface TranscriptionRequest {
  language?: string;
  speakerIdentification?: boolean;
  model?: 'whisper-1';
}

export interface TranscriptionResponse {
  transcriptionId: string;
  status: 'processing' | 'completed' | 'failed';
  estimatedTime?: number;
}

// Analysis types
export interface NoteAnalysis {
  summary: string;
  keyPoints: string[];
  actionItems: ActionItem[];
  sentiment: SentimentAnalysis;
  topics: Topic[];
  entities: Entity[];
  decisions: Decision[];
  questions: Question[];
}

export interface ActionItem {
  id: string;
  text: string;
  assignee?: string;
  dueDate?: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in-progress' | 'completed';
  createdAt: string;
}

export interface SentimentAnalysis {
  overall: 'positive' | 'neutral' | 'negative';
  score: number;
  segments: SentimentSegment[];
}

export interface SentimentSegment {
  startTime: number;
  endTime: number;
  sentiment: 'positive' | 'neutral' | 'negative';
  score: number;
}

export interface Topic {
  name: string;
  confidence: number;
  mentions: number;
  segments: number[];
}

export interface Entity {
  text: string;
  type: 'person' | 'organization' | 'location' | 'date' | 'money' | 'other';
  confidence: number;
  mentions: EntityMention[];
}

export interface EntityMention {
  startTime: number;
  endTime: number;
  segmentId: string;
}

export interface Decision {
  text: string;
  participants: string[];
  timestamp: number;
  confidence: number;
}

export interface Question {
  text: string;
  speaker?: string;
  timestamp: number;
  answered: boolean;
  answer?: string;
}

export interface AnalysisRequest {
  features: ('summary' | 'actionItems' | 'sentiment' | 'topics' | 'entities' | 'decisions' | 'questions')[];
}

export interface AnalysisResponse {
  analysisId: string;
  status: 'processing' | 'completed' | 'failed';
  estimatedTime?: number;
}

// Search types
export interface SearchRequest {
  q: string;
  category?: NoteCategory;
  tags?: string[];
  dateFrom?: string;
  dateTo?: string;
  hasAudio?: boolean;
  sentiment?: ('positive' | 'neutral' | 'negative')[];
  page?: number;
  limit?: number;
  sort?: 'relevance' | 'date' | 'title' | 'duration';
  order?: 'asc' | 'desc';
}

export interface SearchResult {
  note: Note;
  score: number;
  highlights: SearchHighlight[];
}

export interface SearchHighlight {
  field: string;
  text: string;
  startIndex: number;
  endIndex: number;
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  page: number;
  limit: number;
  facets: SearchFacets;
}

export interface SearchFacets {
  categories: FacetCount[];
  tags: FacetCount[];
  speakers: FacetCount[];
  dateRanges: FacetCount[];
  sentiments: FacetCount[];
}

export interface FacetCount {
  value: string;
  count: number;
}

// WebSocket types
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  id?: string;
}

export interface TranscriptionUpdate {
  sessionId: string;
  text: string;
  isFinal: boolean;
  confidence: number;
  speaker?: string;
  timestamp: number;
}

export interface ProcessingUpdate {
  noteId: string;
  stage: 'transcription' | 'analysis' | 'summary' | 'complete';
  progress: number;
  message?: string;
  error?: string;
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// HTTP status codes
export enum HttpStatus {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503
}

// Error codes
export enum ErrorCode {
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  TRANSCRIPTION_FAILED = 'TRANSCRIPTION_FAILED',
  AI_SERVICE_UNAVAILABLE = 'AI_SERVICE_UNAVAILABLE',
  AUDIO_PROCESSING_ERROR = 'AUDIO_PROCESSING_ERROR',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  UNSUPPORTED_FORMAT = 'UNSUPPORTED_FORMAT'
}
