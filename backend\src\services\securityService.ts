import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';
import bcrypt from 'bcryptjs';
import { logger } from '../utils/logger';
import { Redis } from 'ioredis';

const prisma = new PrismaClient();
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

export interface SecurityEvent {
  id: string;
  type: 'login_attempt' | 'failed_login' | 'suspicious_activity' | 'rate_limit_exceeded' | 'unauthorized_access' | 'data_breach_attempt';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  ip: string;
  userAgent: string;
  details: Record<string, any>;
  timestamp: Date;
}

export interface ThreatIntelligence {
  ip: string;
  riskScore: number;
  country: string;
  isVpn: boolean;
  isTor: boolean;
  isProxy: boolean;
  reputation: 'good' | 'suspicious' | 'malicious';
  lastSeen: Date;
}

export class SecurityService {
  private suspiciousIPs = new Set<string>();
  private blockedIPs = new Set<string>();
  private failedLoginAttempts = new Map<string, number>();

  constructor() {
    this.initializeSecurityMonitoring();
  }

  /**
   * Initialize security monitoring
   */
  private initializeSecurityMonitoring(): void {
    // Clean up old failed login attempts every hour
    setInterval(() => {
      this.cleanupFailedAttempts();
    }, 60 * 60 * 1000);

    // Update threat intelligence every 6 hours
    setInterval(() => {
      this.updateThreatIntelligence();
    }, 6 * 60 * 60 * 1000);

    logger.info('Security monitoring initialized');
  }

  /**
   * Log security event
   */
  async logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp'>): Promise<void> {
    const securityEvent: SecurityEvent = {
      id: crypto.randomUUID(),
      ...event,
      timestamp: new Date()
    };

    try {
      // Store in Redis for real-time monitoring
      await redis.lpush('security_events', JSON.stringify(securityEvent));
      await redis.ltrim('security_events', 0, 999); // Keep last 1000 events

      // Log to application logs
      logger.warn('Security event', securityEvent);

      // Handle critical events immediately
      if (event.severity === 'critical') {
        await this.handleCriticalSecurityEvent(securityEvent);
      }

      // Update IP reputation
      await this.updateIPReputation(event.ip, event.type, event.severity);

    } catch (error) {
      logger.error('Failed to log security event', {
        error: error.message,
        event: securityEvent
      });
    }
  }

  /**
   * Validate login attempt
   */
  async validateLoginAttempt(
    email: string,
    password: string,
    ip: string,
    userAgent: string
  ): Promise<{
    isValid: boolean;
    user?: any;
    securityFlags: string[];
  }> {
    const securityFlags: string[] = [];

    try {
      // Check if IP is blocked
      if (this.blockedIPs.has(ip)) {
        await this.logSecurityEvent({
          type: 'unauthorized_access',
          severity: 'high',
          ip,
          userAgent,
          details: { reason: 'blocked_ip', email }
        });
        return { isValid: false, securityFlags: ['blocked_ip'] };
      }

      // Check failed login attempts
      const failedAttempts = this.failedLoginAttempts.get(ip) || 0;
      if (failedAttempts >= 5) {
        await this.logSecurityEvent({
          type: 'rate_limit_exceeded',
          severity: 'medium',
          ip,
          userAgent,
          details: { reason: 'too_many_failed_attempts', email, attempts: failedAttempts }
        });
        return { isValid: false, securityFlags: ['too_many_attempts'] };
      }

      // Find user
      const user = await prisma.user.findUnique({
        where: { email }
      });

      if (!user) {
        this.recordFailedAttempt(ip);
        await this.logSecurityEvent({
          type: 'failed_login',
          severity: 'low',
          ip,
          userAgent,
          details: { reason: 'user_not_found', email }
        });
        return { isValid: false, securityFlags: ['invalid_credentials'] };
      }

      // Validate password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        this.recordFailedAttempt(ip);
        await this.logSecurityEvent({
          type: 'failed_login',
          severity: 'low',
          userId: user.id,
          ip,
          userAgent,
          details: { reason: 'invalid_password', email }
        });
        return { isValid: false, securityFlags: ['invalid_credentials'] };
      }

      // Check for suspicious activity
      const suspiciousFlags = await this.checkSuspiciousActivity(user.id, ip, userAgent);
      securityFlags.push(...suspiciousFlags);

      // Clear failed attempts on successful login
      this.failedLoginAttempts.delete(ip);

      // Log successful login
      await this.logSecurityEvent({
        type: 'login_attempt',
        severity: 'low',
        userId: user.id,
        ip,
        userAgent,
        details: { email, securityFlags }
      });

      return {
        isValid: true,
        user,
        securityFlags
      };

    } catch (error) {
      logger.error('Login validation failed', {
        error: error.message,
        email,
        ip
      });
      return { isValid: false, securityFlags: ['system_error'] };
    }
  }

  /**
   * Check for suspicious activity
   */
  private async checkSuspiciousActivity(
    userId: string,
    ip: string,
    userAgent: string
  ): Promise<string[]> {
    const flags: string[] = [];

    try {
      // Check for multiple IPs in short time
      const recentLogins = await redis.lrange(`user_logins:${userId}`, 0, 9);
      const uniqueIPs = new Set(recentLogins.map(login => JSON.parse(login).ip));
      
      if (uniqueIPs.size > 3) {
        flags.push('multiple_ips');
      }

      // Check for unusual location (simplified)
      const threatIntel = await this.getThreatIntelligence(ip);
      if (threatIntel && threatIntel.riskScore > 0.7) {
        flags.push('high_risk_ip');
      }

      // Check for bot-like behavior
      if (this.isBotUserAgent(userAgent)) {
        flags.push('bot_like_behavior');
      }

      // Store login info for future analysis
      await redis.lpush(`user_logins:${userId}`, JSON.stringify({
        ip,
        userAgent,
        timestamp: new Date().toISOString()
      }));
      await redis.ltrim(`user_logins:${userId}`, 0, 9); // Keep last 10 logins

      if (flags.length > 0) {
        await this.logSecurityEvent({
          type: 'suspicious_activity',
          severity: flags.includes('high_risk_ip') ? 'high' : 'medium',
          userId,
          ip,
          userAgent,
          details: { flags }
        });
      }

    } catch (error) {
      logger.error('Failed to check suspicious activity', {
        error: error.message,
        userId,
        ip
      });
    }

    return flags;
  }

  /**
   * Handle critical security events
   */
  private async handleCriticalSecurityEvent(event: SecurityEvent): Promise<void> {
    try {
      // Block IP for critical events
      this.blockedIPs.add(event.ip);
      await redis.sadd('blocked_ips', event.ip);

      // Set expiration for IP block (24 hours)
      await redis.expire('blocked_ips', 24 * 60 * 60);

      // Notify security team (in production, integrate with alerting system)
      logger.error('CRITICAL SECURITY EVENT', {
        event,
        action: 'ip_blocked'
      });

      // If user is involved, consider account suspension
      if (event.userId) {
        await this.considerAccountSuspension(event.userId, event);
      }

    } catch (error) {
      logger.error('Failed to handle critical security event', {
        error: error.message,
        event
      });
    }
  }

  /**
   * Consider account suspension for security reasons
   */
  private async considerAccountSuspension(userId: string, event: SecurityEvent): Promise<void> {
    try {
      // Get recent security events for this user
      const recentEvents = await this.getRecentSecurityEvents(userId, 24); // Last 24 hours
      const criticalEvents = recentEvents.filter(e => e.severity === 'critical');

      if (criticalEvents.length >= 3) {
        // Suspend account
        await prisma.user.update({
          where: { id: userId },
          data: { status: 'SUSPENDED' }
        });

        logger.error('Account suspended due to security concerns', {
          userId,
          criticalEventsCount: criticalEvents.length,
          triggeringEvent: event
        });
      }

    } catch (error) {
      logger.error('Failed to consider account suspension', {
        error: error.message,
        userId,
        event
      });
    }
  }

  /**
   * Get recent security events for a user
   */
  private async getRecentSecurityEvents(userId: string, hours: number): Promise<SecurityEvent[]> {
    try {
      const events = await redis.lrange('security_events', 0, -1);
      const parsedEvents = events.map(event => JSON.parse(event) as SecurityEvent);
      
      const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
      
      return parsedEvents.filter(event => 
        event.userId === userId && 
        new Date(event.timestamp) > cutoffTime
      );

    } catch (error) {
      logger.error('Failed to get recent security events', {
        error: error.message,
        userId
      });
      return [];
    }
  }

  /**
   * Record failed login attempt
   */
  private recordFailedAttempt(ip: string): void {
    const current = this.failedLoginAttempts.get(ip) || 0;
    this.failedLoginAttempts.set(ip, current + 1);
  }

  /**
   * Clean up old failed login attempts
   */
  private cleanupFailedAttempts(): void {
    // In a production system, you'd want to use Redis with TTL
    // For now, we'll just clear the map periodically
    this.failedLoginAttempts.clear();
    logger.debug('Cleaned up failed login attempts');
  }

  /**
   * Update threat intelligence
   */
  private async updateThreatIntelligence(): Promise<void> {
    try {
      // In production, you would integrate with threat intelligence services like:
      // - VirusTotal
      // - AbuseIPDB
      // - MaxMind GeoIP
      // - Custom threat feeds

      logger.info('Threat intelligence updated');
    } catch (error) {
      logger.error('Failed to update threat intelligence', {
        error: error.message
      });
    }
  }

  /**
   * Get threat intelligence for IP
   */
  private async getThreatIntelligence(ip: string): Promise<ThreatIntelligence | null> {
    try {
      // Simplified threat intelligence
      // In production, query actual threat intelligence services
      
      const riskScore = this.suspiciousIPs.has(ip) ? 0.8 : Math.random() * 0.3;
      
      return {
        ip,
        riskScore,
        country: 'Unknown',
        isVpn: false,
        isTor: false,
        isProxy: false,
        reputation: riskScore > 0.7 ? 'malicious' : riskScore > 0.4 ? 'suspicious' : 'good',
        lastSeen: new Date()
      };

    } catch (error) {
      logger.error('Failed to get threat intelligence', {
        error: error.message,
        ip
      });
      return null;
    }
  }

  /**
   * Update IP reputation based on activity
   */
  private async updateIPReputation(ip: string, eventType: string, severity: string): Promise<void> {
    try {
      if (severity === 'high' || severity === 'critical') {
        this.suspiciousIPs.add(ip);
        await redis.sadd('suspicious_ips', ip);
      }

      // Store IP activity for analysis
      await redis.hincrby(`ip_activity:${ip}`, eventType, 1);
      await redis.expire(`ip_activity:${ip}`, 7 * 24 * 60 * 60); // 7 days

    } catch (error) {
      logger.error('Failed to update IP reputation', {
        error: error.message,
        ip,
        eventType,
        severity
      });
    }
  }

  /**
   * Check if user agent appears to be a bot
   */
  private isBotUserAgent(userAgent: string): boolean {
    const botPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /curl/i,
      /wget/i,
      /python/i,
      /java/i
    ];

    return botPatterns.some(pattern => pattern.test(userAgent));
  }

  /**
   * Get security dashboard data
   */
  public async getSecurityDashboard(): Promise<{
    recentEvents: SecurityEvent[];
    blockedIPs: string[];
    suspiciousIPs: string[];
    threatLevel: 'low' | 'medium' | 'high' | 'critical';
    stats: {
      totalEvents: number;
      criticalEvents: number;
      blockedIPs: number;
      suspiciousActivity: number;
    };
  }> {
    try {
      const recentEventsData = await redis.lrange('security_events', 0, 49); // Last 50 events
      const recentEvents = recentEventsData.map(event => JSON.parse(event) as SecurityEvent);
      
      const blockedIPsArray = await redis.smembers('blocked_ips');
      const suspiciousIPsArray = Array.from(this.suspiciousIPs);

      const criticalEvents = recentEvents.filter(e => e.severity === 'critical').length;
      const suspiciousActivity = recentEvents.filter(e => e.type === 'suspicious_activity').length;

      let threatLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
      if (criticalEvents > 5) threatLevel = 'critical';
      else if (criticalEvents > 2 || suspiciousActivity > 10) threatLevel = 'high';
      else if (criticalEvents > 0 || suspiciousActivity > 5) threatLevel = 'medium';

      return {
        recentEvents: recentEvents.slice(0, 20), // Return top 20
        blockedIPs: blockedIPsArray,
        suspiciousIPs: suspiciousIPsArray,
        threatLevel,
        stats: {
          totalEvents: recentEvents.length,
          criticalEvents,
          blockedIPs: blockedIPsArray.length,
          suspiciousActivity
        }
      };

    } catch (error) {
      logger.error('Failed to get security dashboard', {
        error: error.message
      });
      
      return {
        recentEvents: [],
        blockedIPs: [],
        suspiciousIPs: [],
        threatLevel: 'low',
        stats: {
          totalEvents: 0,
          criticalEvents: 0,
          blockedIPs: 0,
          suspiciousActivity: 0
        }
      };
    }
  }

  /**
   * Manually block IP address
   */
  public async blockIP(ip: string, reason: string, duration?: number): Promise<void> {
    try {
      this.blockedIPs.add(ip);
      await redis.sadd('blocked_ips', ip);
      
      if (duration) {
        await redis.expire('blocked_ips', duration);
      }

      await this.logSecurityEvent({
        type: 'unauthorized_access',
        severity: 'high',
        ip,
        userAgent: 'Manual Block',
        details: { reason, duration, action: 'manual_block' }
      });

      logger.info('IP manually blocked', { ip, reason, duration });

    } catch (error) {
      logger.error('Failed to block IP', {
        error: error.message,
        ip,
        reason
      });
      throw error;
    }
  }

  /**
   * Unblock IP address
   */
  public async unblockIP(ip: string, reason: string): Promise<void> {
    try {
      this.blockedIPs.delete(ip);
      await redis.srem('blocked_ips', ip);

      logger.info('IP unblocked', { ip, reason });

    } catch (error) {
      logger.error('Failed to unblock IP', {
        error: error.message,
        ip,
        reason
      });
      throw error;
    }
  }
}

export default new SecurityService();
