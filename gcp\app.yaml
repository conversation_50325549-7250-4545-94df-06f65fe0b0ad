# Google App Engine configuration for <PERSON><PERSON>ap<PERSON> backend

runtime: nodejs18

# Automatic scaling configuration
automatic_scaling:
  min_instances: 1
  max_instances: 10
  target_cpu_utilization: 0.6
  target_throughput_utilization: 0.6
  max_concurrent_requests: 80

# Resource allocation
resources:
  cpu: 1
  memory_gb: 2
  disk_size_gb: 10

# Network configuration
network:
  forwarded_ports:
    - 3001

# Environment variables
env_variables:
  NODE_ENV: production
  PORT: 3001
  
  # Database configuration
  DATABASE_URL: ${DATABASE_URL}
  
  # Redis configuration
  REDIS_URL: ${REDIS_URL}
  
  # JWT secrets
  JWT_SECRET: ${JWT_SECRET}
  JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
  
  # OpenAI configuration
  OPENAI_API_KEY: ${OPENAI_API_KEY}
  
  # CORS configuration
  CORS_ORIGIN: https://quickcapt.com
  ALLOWED_ORIGINS: https://quickcapt.com,https://www.quickcapt.com
  
  # Rate limiting
  RATE_LIMIT_WINDOW_MS: 900000
  RATE_LIMIT_MAX_REQUESTS: 100
  
  # File upload configuration
  MAX_FILE_SIZE: 104857600
  UPLOAD_PATH: /tmp/uploads
  
  # Email configuration
  SMTP_HOST: ${SMTP_HOST}
  SMTP_PORT: ${SMTP_PORT}
  SMTP_USER: ${SMTP_USER}
  SMTP_PASS: ${SMTP_PASS}
  
  # Monitoring
  GOOGLE_CLOUD_PROJECT: ${GOOGLE_CLOUD_PROJECT}
  
  # Feature flags
  ENABLE_ANALYTICS: true
  ENABLE_MONITORING: true
  ENABLE_CACHING: true

# Health check configuration
readiness_check:
  path: "/health"
  check_interval_sec: 5
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 2
  app_start_timeout_sec: 300

liveness_check:
  path: "/health"
  check_interval_sec: 30
  timeout_sec: 4
  failure_threshold: 4
  success_threshold: 2

# Handlers for static files and routing
handlers:
  # Health check endpoint
  - url: /health
    script: auto
    secure: always
    
  # API endpoints
  - url: /api/.*
    script: auto
    secure: always
    
  # WebSocket endpoints
  - url: /socket.io/.*
    script: auto
    secure: always
    
  # All other requests
  - url: /.*
    script: auto
    secure: always

# Skip files during deployment
skip_files:
  - ^(.*/)?#.*#$
  - ^(.*/)?.*~$
  - ^(.*/)?.*\.py[co]$
  - ^(.*/)?.*/RCS/.*$
  - ^(.*/)?\..*$
  - ^(.*/)?tests?/.*$
  - ^(.*/)?test/.*$
  - ^(.*/)?.*\.md$
  - ^(.*/)?.*\.txt$
  - ^(.*/)?Dockerfile.*$
  - ^(.*/)?docker-compose.*$
  - ^(.*/)?\.dockerignore$
  - ^(.*/)?\.gitignore$
  - ^(.*/)?\.git/.*$
  - ^(.*/)?node_modules/.*$
  - ^(.*/)?coverage/.*$
  - ^(.*/)?\.nyc_output/.*$

# Beta settings
beta_settings:
  # Enable Cloud SQL connections
  cloud_sql_instances: ${GOOGLE_CLOUD_PROJECT}:${REGION}:${SQL_INSTANCE_NAME}

# VPC access connector (if using VPC)
vpc_access_connector:
  name: projects/${GOOGLE_CLOUD_PROJECT}/locations/${REGION}/connectors/quickcapt-connector

# Entrypoint
entrypoint: npm start

# Build configuration
build_env_variables:
  NODE_ENV: production
