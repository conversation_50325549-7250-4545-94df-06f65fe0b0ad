import React from 'react';
import { motion } from 'framer-motion';
import {
  CalendarIcon,
  TagIcon,
  SpeakerWaveIcon,
  BookmarkIcon,
  ShareIcon,
  EllipsisHorizontalIcon
} from '@heroicons/react/24/outline';
import { BookmarkIcon as BookmarkSolidIcon } from '@heroicons/react/24/solid';

import { Note } from '../../types';
import { useNotes } from '../../hooks/useNotes';

interface NoteCardProps {
  note: Note;
  viewMode?: 'grid' | 'list';
  onClick?: (note: Note) => void;
  onEdit?: (note: Note) => void;
  className?: string;
}

const categoryColors = {
  MEETING: 'bg-blue-100 text-blue-800',
  LECTURE: 'bg-green-100 text-green-800',
  INTERVIEW: 'bg-purple-100 text-purple-800',
  PERSONAL: 'bg-yellow-100 text-yellow-800',
  OTHER: 'bg-gray-100 text-gray-800'
};

const NoteCard: React.FC<NoteCardProps> = ({
  note,
  viewMode = 'grid',
  onClick,
  onEdit,
  className = ''
}) => {
  const { toggleFavorite, archiveNote } = useNotes();

  const handleToggleFavorite = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await toggleFavorite(note.id);
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    }
  };

  const handleArchive = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await archiveNote(note.id, !note.isArchived);
    } catch (error) {
      console.error('Failed to archive note:', error);
    }
  };

  const handleClick = () => {
    onClick?.(note);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.(note);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const truncateContent = (content: string, maxLength: number) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength).trim() + '...';
  };

  if (viewMode === 'list') {
    return (
      <motion.div
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
        onClick={handleClick}
        className={`bg-white rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all cursor-pointer ${className}`}
      >
        <div className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-2">
                <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${categoryColors[note.category]}`}>
                  {note.category.toLowerCase()}
                </span>
                {note.audioFile && (
                  <SpeakerWaveIcon className="h-4 w-4 text-gray-400" title="Has audio" />
                )}
                {note.isPublic && (
                  <ShareIcon className="h-4 w-4 text-blue-500" title="Public note" />
                )}
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 truncate mb-1">
                {note.title}
              </h3>
              
              <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                {truncateContent(note.content, 150)}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 text-xs text-gray-500">
                  <div className="flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-1" />
                    {formatDate(note.updatedAt)}
                  </div>
                  {note.tags.length > 0 && (
                    <div className="flex items-center">
                      <TagIcon className="h-4 w-4 mr-1" />
                      {note.tags.slice(0, 2).join(', ')}
                      {note.tags.length > 2 && ` +${note.tags.length - 2}`}
                    </div>
                  )}
                </div>
                
                <div className="flex items-center space-x-1">
                  <button
                    onClick={handleToggleFavorite}
                    className="p-1 text-gray-400 hover:text-yellow-500 transition-colors"
                  >
                    {note.isFavorite ? (
                      <BookmarkSolidIcon className="h-4 w-4 text-yellow-500" />
                    ) : (
                      <BookmarkIcon className="h-4 w-4" />
                    )}
                  </button>
                  
                  <button
                    onClick={handleEdit}
                    className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <EllipsisHorizontalIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={handleClick}
      className={`bg-white rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-lg transition-all cursor-pointer ${className}`}
    >
      <div className="p-4">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-2">
            <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${categoryColors[note.category]}`}>
              {note.category.toLowerCase()}
            </span>
            {note.audioFile && (
              <SpeakerWaveIcon className="h-4 w-4 text-gray-400" title="Has audio" />
            )}
            {note.isPublic && (
              <ShareIcon className="h-4 w-4 text-blue-500" title="Public note" />
            )}
          </div>
          
          <div className="flex items-center space-x-1">
            <button
              onClick={handleToggleFavorite}
              className="p-1 text-gray-400 hover:text-yellow-500 transition-colors"
            >
              {note.isFavorite ? (
                <BookmarkSolidIcon className="h-4 w-4 text-yellow-500" />
              ) : (
                <BookmarkIcon className="h-4 w-4" />
              )}
            </button>
            
            <button
              onClick={handleEdit}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <EllipsisHorizontalIcon className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Title */}
        <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
          {note.title}
        </h3>

        {/* Content Preview */}
        <p className="text-gray-600 text-sm line-clamp-3 mb-4">
          {truncateContent(note.content, 120)}
        </p>

        {/* Tags */}
        {note.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {note.tags.slice(0, 3).map(tag => (
              <span
                key={tag}
                className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
              >
                <TagIcon className="h-3 w-3 mr-1" />
                {tag}
              </span>
            ))}
            {note.tags.length > 3 && (
              <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">
                +{note.tags.length - 3} more
              </span>
            )}
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center">
            <CalendarIcon className="h-4 w-4 mr-1" />
            {formatDate(note.updatedAt)}
          </div>
          
          {note.transcription && (
            <div className="flex items-center">
              <div className={`w-2 h-2 rounded-full mr-1 ${
                note.transcription.processingStatus === 'COMPLETED' 
                  ? 'bg-green-500' 
                  : note.transcription.processingStatus === 'PROCESSING'
                  ? 'bg-yellow-500'
                  : 'bg-gray-400'
              }`} />
              <span className="capitalize">
                {note.transcription.processingStatus.toLowerCase()}
              </span>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default NoteCard;
