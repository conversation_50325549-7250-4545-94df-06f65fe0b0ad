# QuickCapt - AI-Powered Note Taking Revolution

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5+-blue.svg)](https://www.typescriptlang.org/)

## 🚀 Product Overview

**QuickCapt** is an intelligent AI-powered note-taking application that transforms how you capture, process, and organize meeting insights. With real-time transcription, AI-driven summarization, and smart organization features, QuickCapt ensures no important detail is ever lost.

### 🎯 Key Features

- **Real-time Audio Recording & Transcription** - Multi-language support with speaker identification
- **AI-Powered Processing** - Automatic summarization, action item extraction, and sentiment analysis
- **Smart Organization** - Hierarchical folders with semantic search capabilities
- **Collaboration Tools** - Real-time sharing, comments, and team workspace
- **Export & Integration** - Multiple formats and CRM/project management integrations
- **Privacy-First** - End-to-end encryption with offline processing options

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React PWA] --> B[Audio Recorder]
        A --> C[Real-time Transcription]
        A --> D[Note Editor]
        A --> E[Search Interface]
    end
    
    subgraph "API Gateway"
        F[Express.js API] --> G[Authentication]
        F --> H[Rate Limiting]
        F --> I[WebSocket Server]
    end
    
    subgraph "Core Services"
        J[Audio Processing Service] --> K[Speech-to-Text]
        L[AI Analysis Service] --> M[OpenAI GPT-4]
        N[Search Service] --> O[Elasticsearch]
        P[File Storage Service] --> Q[Local/Cloud Storage]
    end
    
    subgraph "Data Layer"
        R[(PostgreSQL)]
        S[(Redis Cache)]
        T[File System]
    end
    
    A --> F
    F --> J
    F --> L
    F --> N
    F --> P
    J --> R
    L --> R
    N --> O
    P --> T
    F --> S
```

## 🔄 Application Workflow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API Server
    participant AI as AI Service
    participant DB as Database
    
    U->>F: Start Recording
    F->>A: Initialize Session
    A->>DB: Create Note Record
    
    loop Real-time Processing
        F->>A: Stream Audio Chunks
        A->>AI: Process Audio
        AI->>A: Return Transcription
        A->>F: Send Live Text
        F->>U: Display Transcription
    end
    
    U->>F: Stop Recording
    F->>A: Finalize Recording
    A->>AI: Generate Summary
    A->>AI: Extract Action Items
    AI->>A: Return Analysis
    A->>DB: Save Complete Note
    A->>F: Send Final Results
    F->>U: Show Processed Note
```

## 📁 Project Structure

```
quickcapt/
├── frontend/                    # React PWA Application
│   ├── public/
│   │   ├── index.html
│   │   ├── manifest.json
│   │   └── sw.js               # Service Worker
│   ├── src/
│   │   ├── components/
│   │   │   ├── AudioRecorder/
│   │   │   │   ├── AudioRecorder.tsx
│   │   │   │   ├── WaveformVisualizer.tsx
│   │   │   │   └── RecordingControls.tsx
│   │   │   ├── Transcription/
│   │   │   │   ├── TranscriptViewer.tsx
│   │   │   │   ├── SpeakerIdentification.tsx
│   │   │   │   └── LiveTranscription.tsx
│   │   │   ├── Notes/
│   │   │   │   ├── NoteEditor.tsx
│   │   │   │   ├── NotesGrid.tsx
│   │   │   │   └── NoteCard.tsx
│   │   │   ├── Search/
│   │   │   │   ├── SearchInterface.tsx
│   │   │   │   └── SearchResults.tsx
│   │   │   └── Common/
│   │   │       ├── Header.tsx
│   │   │       ├── Sidebar.tsx
│   │   │       └── LoadingSpinner.tsx
│   │   ├── hooks/
│   │   │   ├── useAudioRecording.ts
│   │   │   ├── useWebSocket.ts
│   │   │   └── useLocalStorage.ts
│   │   ├── services/
│   │   │   ├── api.ts
│   │   │   ├── audioProcessor.ts
│   │   │   └── websocket.ts
│   │   ├── utils/
│   │   │   ├── audioUtils.ts
│   │   │   ├── dateUtils.ts
│   │   │   └── formatters.ts
│   │   ├── types/
│   │   │   └── index.ts
│   │   ├── App.tsx
│   │   └── index.tsx
│   ├── package.json
│   └── tsconfig.json
├── backend/                     # Node.js API Server
│   ├── src/
│   │   ├── controllers/
│   │   │   ├── authController.ts
│   │   │   ├── notesController.ts
│   │   │   ├── audioController.ts
│   │   │   └── searchController.ts
│   │   ├── services/
│   │   │   ├── audioProcessingService.ts
│   │   │   ├── aiAnalysisService.ts
│   │   │   ├── transcriptionService.ts
│   │   │   └── searchService.ts
│   │   ├── models/
│   │   │   ├── User.ts
│   │   │   ├── Note.ts
│   │   │   └── AudioFile.ts
│   │   ├── middleware/
│   │   │   ├── auth.ts
│   │   │   ├── rateLimiter.ts
│   │   │   └── errorHandler.ts
│   │   ├── routes/
│   │   │   ├── auth.ts
│   │   │   ├── notes.ts
│   │   │   ├── audio.ts
│   │   │   └── search.ts
│   │   ├── utils/
│   │   │   ├── logger.ts
│   │   │   ├── validation.ts
│   │   │   └── encryption.ts
│   │   ├── config/
│   │   │   ├── database.ts
│   │   │   ├── redis.ts
│   │   │   └── environment.ts
│   │   ├── websocket/
│   │   │   └── socketHandlers.ts
│   │   └── app.ts
│   ├── package.json
│   └── tsconfig.json
├── ai-services/                 # AI Processing Microservices
│   ├── transcription/
│   │   ├── whisperService.ts
│   │   ├── speakerDiarization.ts
│   │   └── languageDetection.ts
│   ├── analysis/
│   │   ├── summaryGenerator.ts
│   │   ├── actionItemExtractor.ts
│   │   └── sentimentAnalyzer.ts
│   └── package.json
├── shared/                      # Shared Types & Utilities
│   ├── types/
│   │   ├── api.ts
│   │   ├── note.ts
│   │   └── user.ts
│   └── utils/
│       ├── constants.ts
│       └── validators.ts
├── docker/                      # Docker Configuration
│   ├── Dockerfile.frontend
│   ├── Dockerfile.backend
│   ├── Dockerfile.ai-services
│   └── docker-compose.yml
├── docs/                        # Documentation
│   ├── API.md
│   ├── DEPLOYMENT.md
│   └── CONTRIBUTING.md
├── scripts/                     # Build & Deployment Scripts
│   ├── build.sh
│   ├── deploy.sh
│   └── setup-dev.sh
├── .env.example
├── .gitignore
├── package.json
└── README.md
```

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **WebRTC** for audio recording
- **WebSocket** for real-time updates
- **PWA** capabilities with service workers

### Backend
- **Node.js** with Express.js
- **TypeScript** for type safety
- **PostgreSQL** for data persistence
- **Redis** for caching and sessions
- **Socket.io** for real-time communication

### AI & Processing
- **OpenAI Whisper** for speech-to-text
- **OpenAI GPT-4** for text analysis
- **Custom algorithms** for speaker diarization
- **Web Audio API** for audio processing

## 🚀 Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/HectorTa1989/QuickCapt.git
   cd QuickCapt
   ```

2. **Install dependencies**
   ```bash
   npm run install:all
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start development servers**
   ```bash
   npm run dev
   ```

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Please read [CONTRIBUTING.md](docs/CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📧 Contact

- **Author**: Hector Ta
- **GitHub**: [@HectorTa1989](https://github.com/HectorTa1989)
- **Project**: [QuickCapt](https://github.com/HectorTa1989/QuickCapt)
