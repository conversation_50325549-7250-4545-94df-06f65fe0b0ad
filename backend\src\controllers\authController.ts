import { Request, Response, NextFunction } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { body, validationResult } from 'express-validator';
import rateLimit from 'express-rate-limit';
import { AuthService } from '../services/authService';
import { UserService } from '../services/userService';
import { logger } from '../utils/logger';
import { ApiResponse, User, AuthResponse } from '../types';

// Rate limiting for auth endpoints
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many authentication attempts, please try again later.',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

export class AuthController {
  private authService: AuthService;
  private userService: UserService;

  constructor() {
    this.authService = new AuthService();
    this.userService = new UserService();
  }

  // Validation rules
  public registerValidation = [
    body('name')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Name must be between 2 and 50 characters'),
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email'),
    body('password')
      .isLength({ min: 8, max: 128 })
      .withMessage('Password must be between 8 and 128 characters')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  ];

  public loginValidation = [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email'),
    body('password')
      .notEmpty()
      .withMessage('Password is required'),
  ];

  public refreshTokenValidation = [
    body('refreshToken')
      .notEmpty()
      .withMessage('Refresh token is required'),
  ];

  // Register new user
  public register = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid input data',
            details: errors.array()
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      const { name, email, password } = req.body;

      // Check if user already exists
      const existingUser = await this.userService.findByEmail(email);
      if (existingUser) {
        res.status(409).json({
          success: false,
          error: {
            code: 'USER_EXISTS',
            message: 'User with this email already exists'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      // Hash password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // Create user
      const user = await this.userService.create({
        name,
        email,
        password: hashedPassword,
        preferences: {
          theme: 'system',
          language: 'en',
          autoSave: true,
          notifications: {
            email: true,
            push: false,
            transcriptionComplete: true,
            summaryReady: true,
            actionItemReminders: true
          },
          audioQuality: 'high',
          transcriptionLanguage: 'en',
          speakerIdentification: true
        },
        subscription: {
          type: 'free',
          status: 'active',
          features: ['basic_transcription', 'simple_notes', 'limited_search'],
          limits: {
            monthlyMinutes: 100,
            storageGB: 1,
            collaborators: 0
          }
        }
      });

      // Generate tokens
      const { accessToken, refreshToken } = await this.authService.generateTokens(user.id);

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user;

      const authResponse: AuthResponse = {
        user: userWithoutPassword as User,
        token: accessToken,
        refreshToken,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
      };

      logger.info(`User registered successfully: ${email}`, { userId: user.id });

      res.status(201).json({
        success: true,
        data: authResponse,
        message: 'User registered successfully',
        timestamp: new Date().toISOString()
      } as ApiResponse<AuthResponse>);

    } catch (error) {
      logger.error('Registration error:', error);
      next(error);
    }
  };

  // Login user
  public login = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid input data',
            details: errors.array()
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      const { email, password, rememberMe = false } = req.body;

      // Find user
      const user = await this.userService.findByEmail(email);
      if (!user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid email or password'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      // Check password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid email or password'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      // Update last login
      await this.userService.updateLastLogin(user.id);

      // Generate tokens
      const tokenExpiry = rememberMe ? '30d' : '7d';
      const { accessToken, refreshToken } = await this.authService.generateTokens(user.id, tokenExpiry);

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user;

      const authResponse: AuthResponse = {
        user: userWithoutPassword as User,
        token: accessToken,
        refreshToken,
        expiresAt: new Date(Date.now() + (rememberMe ? 30 : 7) * 24 * 60 * 60 * 1000).toISOString()
      };

      logger.info(`User logged in successfully: ${email}`, { userId: user.id });

      res.status(200).json({
        success: true,
        data: authResponse,
        message: 'Login successful',
        timestamp: new Date().toISOString()
      } as ApiResponse<AuthResponse>);

    } catch (error) {
      logger.error('Login error:', error);
      next(error);
    }
  };

  // Refresh access token
  public refreshToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid input data',
            details: errors.array()
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      const { refreshToken } = req.body;

      // Verify refresh token
      const decoded = await this.authService.verifyRefreshToken(refreshToken);
      if (!decoded) {
        res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_TOKEN',
            message: 'Invalid or expired refresh token'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      // Get user
      const user = await this.userService.findById(decoded.userId);
      if (!user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      // Generate new tokens
      const { accessToken, refreshToken: newRefreshToken } = await this.authService.generateTokens(user.id);

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user;

      const authResponse: AuthResponse = {
        user: userWithoutPassword as User,
        token: accessToken,
        refreshToken: newRefreshToken,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      };

      res.status(200).json({
        success: true,
        data: authResponse,
        message: 'Token refreshed successfully',
        timestamp: new Date().toISOString()
      } as ApiResponse<AuthResponse>);

    } catch (error) {
      logger.error('Token refresh error:', error);
      next(error);
    }
  };

  // Logout user
  public logout = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const token = req.headers.authorization?.replace('Bearer ', '');

      if (userId && token) {
        // Invalidate token (add to blacklist)
        await this.authService.invalidateToken(token);
        logger.info(`User logged out: ${userId}`);
      }

      res.status(200).json({
        success: true,
        message: 'Logout successful',
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      logger.error('Logout error:', error);
      next(error);
    }
  };

  // Get current user profile
  public getProfile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'User not authenticated'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      const user = await this.userService.findById(userId);
      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user;

      res.status(200).json({
        success: true,
        data: { user: userWithoutPassword },
        timestamp: new Date().toISOString()
      } as ApiResponse<{ user: User }>);

    } catch (error) {
      logger.error('Get profile error:', error);
      next(error);
    }
  };

  // Update user profile
  public updateProfile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'User not authenticated'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      const updates = req.body;
      
      // Remove sensitive fields that shouldn't be updated via this endpoint
      delete updates.password;
      delete updates.email; // Email changes should go through separate verification process
      delete updates.id;

      const updatedUser = await this.userService.update(userId, updates);
      
      // Remove password from response
      const { password: _, ...userWithoutPassword } = updatedUser;

      logger.info(`User profile updated: ${userId}`);

      res.status(200).json({
        success: true,
        data: { user: userWithoutPassword },
        message: 'Profile updated successfully',
        timestamp: new Date().toISOString()
      } as ApiResponse<{ user: User }>);

    } catch (error) {
      logger.error('Update profile error:', error);
      next(error);
    }
  };

  // Change password
  public changePassword = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { currentPassword, newPassword } = req.body;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'User not authenticated'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      // Validate new password
      if (!newPassword || newPassword.length < 8) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'New password must be at least 8 characters long'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      const user = await this.userService.findById(userId);
      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_PASSWORD',
            message: 'Current password is incorrect'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      // Hash new password
      const saltRounds = 12;
      const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

      // Update password
      await this.userService.updatePassword(userId, hashedNewPassword);

      logger.info(`Password changed for user: ${userId}`);

      res.status(200).json({
        success: true,
        message: 'Password changed successfully',
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      logger.error('Change password error:', error);
      next(error);
    }
  };
}

export default AuthController;
