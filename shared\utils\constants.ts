// Shared constants between frontend and backend

// Application constants
export const APP_NAME = 'QuickCapt';
export const APP_VERSION = '1.0.0';
export const APP_DESCRIPTION = 'AI-Powered Note Taking Revolution';

// API configuration
export const API_VERSION = 'v1';
export const API_BASE_PATH = '/api';

// Authentication
export const JWT_EXPIRY = '7d';
export const REFRESH_TOKEN_EXPIRY = '30d';
export const PASSWORD_MIN_LENGTH = 8;
export const PASSWORD_MAX_LENGTH = 128;

// File upload limits
export const MAX_AUDIO_FILE_SIZE = 100 * 1024 * 1024; // 100MB
export const MAX_AVATAR_FILE_SIZE = 5 * 1024 * 1024; // 5MB
export const SUPPORTED_AUDIO_FORMATS = [
  'audio/wav',
  'audio/mp3',
  'audio/mpeg',
  'audio/m4a',
  'audio/webm',
  'audio/ogg',
  'audio/flac'
];
export const SUPPORTED_IMAGE_FORMATS = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp'
];

// Audio processing
export const AUDIO_SAMPLE_RATES = [8000, 16000, 22050, 44100, 48000];
export const DEFAULT_SAMPLE_RATE = 44100;
export const AUDIO_CHANNELS = {
  MONO: 1,
  STEREO: 2
};

// Transcription
export const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'es', name: 'Spanish' },
  { code: 'fr', name: 'French' },
  { code: 'de', name: 'German' },
  { code: 'it', name: 'Italian' },
  { code: 'pt', name: 'Portuguese' },
  { code: 'ru', name: 'Russian' },
  { code: 'ja', name: 'Japanese' },
  { code: 'ko', name: 'Korean' },
  { code: 'zh', name: 'Chinese' },
  { code: 'ar', name: 'Arabic' },
  { code: 'hi', name: 'Hindi' },
  { code: 'nl', name: 'Dutch' },
  { code: 'sv', name: 'Swedish' },
  { code: 'da', name: 'Danish' },
  { code: 'no', name: 'Norwegian' },
  { code: 'fi', name: 'Finnish' },
  { code: 'pl', name: 'Polish' },
  { code: 'tr', name: 'Turkish' },
  { code: 'uk', name: 'Ukrainian' },
  { code: 'cs', name: 'Czech' },
  { code: 'hu', name: 'Hungarian' },
  { code: 'ro', name: 'Romanian' },
  { code: 'bg', name: 'Bulgarian' },
  { code: 'hr', name: 'Croatian' },
  { code: 'sk', name: 'Slovak' },
  { code: 'sl', name: 'Slovenian' },
  { code: 'et', name: 'Estonian' },
  { code: 'lv', name: 'Latvian' },
  { code: 'lt', name: 'Lithuanian' }
];

export const DEFAULT_LANGUAGE = 'en';

// Note categories
export const NOTE_CATEGORIES = [
  { value: 'meeting', label: 'Meeting', icon: '👥' },
  { value: 'lecture', label: 'Lecture', icon: '🎓' },
  { value: 'interview', label: 'Interview', icon: '🎤' },
  { value: 'brainstorm', label: 'Brainstorm', icon: '💡' },
  { value: 'call', label: 'Call', icon: '📞' },
  { value: 'presentation', label: 'Presentation', icon: '📊' },
  { value: 'other', label: 'Other', icon: '📝' }
];

// Pagination
export const DEFAULT_PAGE_SIZE = 20;
export const MAX_PAGE_SIZE = 100;
export const MIN_PAGE_SIZE = 1;

// Search
export const MAX_SEARCH_QUERY_LENGTH = 500;
export const SEARCH_DEBOUNCE_MS = 300;
export const MAX_SEARCH_RESULTS = 1000;

// Rate limiting
export const RATE_LIMITS = {
  GENERAL: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // requests per window
  },
  AUTH: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5 // login attempts per window
  },
  UPLOAD: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 10 // uploads per window
  },
  TRANSCRIPTION: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 20 // transcriptions per window
  },
  SEARCH: {
    windowMs: 60 * 1000, // 1 minute
    max: 60 // searches per window
  }
};

// WebSocket events
export const WS_EVENTS = {
  // Connection
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  ERROR: 'error',
  
  // Authentication
  AUTHENTICATE: 'authenticate',
  AUTHENTICATED: 'authenticated',
  
  // Transcription
  START_TRANSCRIPTION: 'start_transcription',
  STOP_TRANSCRIPTION: 'stop_transcription',
  TRANSCRIPTION_UPDATE: 'transcription_update',
  TRANSCRIPTION_COMPLETE: 'transcription_complete',
  TRANSCRIPTION_ERROR: 'transcription_error',
  
  // Processing
  PROCESSING_UPDATE: 'processing_update',
  PROCESSING_COMPLETE: 'processing_complete',
  PROCESSING_ERROR: 'processing_error',
  
  // Collaboration
  JOIN_NOTE: 'join_note',
  LEAVE_NOTE: 'leave_note',
  NOTE_UPDATE: 'note_update',
  USER_TYPING: 'user_typing',
  USER_STOPPED_TYPING: 'user_stopped_typing',
  
  // Notifications
  NOTIFICATION: 'notification',
  
  // Heartbeat
  HEARTBEAT: 'heartbeat',
  HEARTBEAT_RESPONSE: 'heartbeat_response'
};

// Subscription plans
export const SUBSCRIPTION_PLANS = {
  FREE: {
    type: 'free',
    name: 'Free',
    price: 0,
    features: [
      '100 minutes/month',
      'Basic transcription',
      'Simple notes',
      'Limited search'
    ],
    limits: {
      monthlyMinutes: 100,
      storageGB: 1,
      collaborators: 0
    }
  },
  PRO: {
    type: 'pro',
    name: 'Pro',
    price: 15,
    features: [
      'Unlimited recording',
      'Advanced AI features',
      'Speaker identification',
      'Export options',
      'Priority support'
    ],
    limits: {
      monthlyMinutes: -1, // unlimited
      storageGB: 50,
      collaborators: 5
    }
  },
  TEAM: {
    type: 'team',
    name: 'Team',
    price: 30,
    features: [
      'Everything in Pro',
      'Team collaboration',
      'Advanced analytics',
      'Integrations',
      'Admin controls'
    ],
    limits: {
      monthlyMinutes: -1, // unlimited
      storageGB: 200,
      collaborators: 25
    }
  },
  ENTERPRISE: {
    type: 'enterprise',
    name: 'Enterprise',
    price: null, // custom pricing
    features: [
      'Everything in Team',
      'Custom deployment',
      'Advanced security',
      'SLA guarantee',
      'Dedicated support'
    ],
    limits: {
      monthlyMinutes: -1, // unlimited
      storageGB: -1, // unlimited
      collaborators: -1 // unlimited
    }
  }
};

// Themes
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system'
};

// Audio quality settings
export const AUDIO_QUALITY = {
  LOW: {
    value: 'low',
    label: 'Low (32 kbps)',
    bitrate: 32000,
    sampleRate: 22050
  },
  MEDIUM: {
    value: 'medium',
    label: 'Medium (64 kbps)',
    bitrate: 64000,
    sampleRate: 44100
  },
  HIGH: {
    value: 'high',
    label: 'High (128 kbps)',
    bitrate: 128000,
    sampleRate: 44100
  }
};

// Action item priorities
export const ACTION_ITEM_PRIORITIES = {
  LOW: { value: 'low', label: 'Low', color: '#10b981' },
  MEDIUM: { value: 'medium', label: 'Medium', color: '#f59e0b' },
  HIGH: { value: 'high', label: 'High', color: '#ef4444' }
};

// Action item statuses
export const ACTION_ITEM_STATUSES = {
  PENDING: { value: 'pending', label: 'Pending', color: '#6b7280' },
  IN_PROGRESS: { value: 'in-progress', label: 'In Progress', color: '#3b82f6' },
  COMPLETED: { value: 'completed', label: 'Completed', color: '#10b981' }
};

// Sentiment colors
export const SENTIMENT_COLORS = {
  POSITIVE: '#10b981',
  NEUTRAL: '#6b7280',
  NEGATIVE: '#ef4444'
};

// Speaker colors for visualization
export const SPEAKER_COLORS = [
  '#3b82f6', // blue
  '#ef4444', // red
  '#10b981', // green
  '#f59e0b', // yellow
  '#8b5cf6', // purple
  '#06b6d4', // cyan
  '#f97316', // orange
  '#84cc16', // lime
  '#ec4899', // pink
  '#6366f1'  // indigo
];

// Date formats
export const DATE_FORMATS = {
  SHORT: 'MMM d, yyyy',
  LONG: 'MMMM d, yyyy',
  WITH_TIME: 'MMM d, yyyy h:mm a',
  ISO: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx",
  TIME_ONLY: 'h:mm a'
};

// Keyboard shortcuts
export const KEYBOARD_SHORTCUTS = {
  SEARCH: 'cmd+k',
  NEW_RECORDING: 'cmd+r',
  NEW_NOTE: 'cmd+n',
  SAVE: 'cmd+s',
  TOGGLE_SIDEBAR: 'cmd+b',
  TOGGLE_THEME: 'cmd+shift+t'
};

// Cache keys
export const CACHE_KEYS = {
  USER_PREFERENCES: 'user_preferences',
  RECENT_NOTES: 'recent_notes',
  SEARCH_HISTORY: 'search_history',
  AUDIO_SETTINGS: 'audio_settings'
};

// Local storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'quickcapt_token',
  REFRESH_TOKEN: 'quickcapt_refresh_token',
  USER_PREFERENCES: 'quickcapt_preferences',
  THEME: 'quickcapt_theme',
  LANGUAGE: 'quickcapt_language'
};

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FILE_TOO_LARGE: 'File size exceeds the maximum limit.',
  UNSUPPORTED_FORMAT: 'File format is not supported.',
  TRANSCRIPTION_FAILED: 'Transcription failed. Please try again.',
  AI_SERVICE_UNAVAILABLE: 'AI service is temporarily unavailable.',
  GENERIC_ERROR: 'An unexpected error occurred. Please try again.'
};

// Success messages
export const SUCCESS_MESSAGES = {
  NOTE_SAVED: 'Note saved successfully.',
  NOTE_DELETED: 'Note deleted successfully.',
  AUDIO_UPLOADED: 'Audio file uploaded successfully.',
  TRANSCRIPTION_COMPLETE: 'Transcription completed successfully.',
  SETTINGS_UPDATED: 'Settings updated successfully.',
  PASSWORD_CHANGED: 'Password changed successfully.'
};
