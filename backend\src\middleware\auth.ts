import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/authService';
import { UserService } from '../services/userService';
import { logger } from '../utils/logger';
import { ApiResponse } from '../types';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        name: string;
        subscription: {
          type: string;
          features: string[];
          limits: Record<string, any>;
        };
      };
    }
  }
}

export class AuthMiddleware {
  private authService: AuthService;
  private userService: UserService;

  constructor() {
    this.authService = new AuthService();
    this.userService = new UserService();
  }

  /**
   * Middleware to authenticate requests using JWT tokens
   */
  public authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
          success: false,
          error: {
            code: 'MISSING_TOKEN',
            message: 'Authorization token is required'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix

      // Verify token
      const decoded = await this.authService.verifyAccessToken(token);
      if (!decoded) {
        res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_TOKEN',
            message: 'Invalid or expired token'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      // Get user details
      const user = await this.userService.findById(decoded.userId);
      if (!user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      // Check if user account is active
      if (user.status === 'suspended' || user.status === 'deleted') {
        res.status(403).json({
          success: false,
          error: {
            code: 'ACCOUNT_SUSPENDED',
            message: 'Account is suspended or deactivated'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      // Attach user to request
      req.user = {
        id: user.id,
        email: user.email,
        name: user.name,
        subscription: user.subscription
      };

      next();

    } catch (error) {
      logger.error('Authentication error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_ERROR',
          message: 'Authentication failed'
        },
        timestamp: new Date().toISOString()
      } as ApiResponse);
    }
  };

  /**
   * Optional authentication - doesn't fail if no token provided
   */
  public optionalAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        // No token provided, continue without authentication
        next();
        return;
      }

      const token = authHeader.substring(7);
      const decoded = await this.authService.verifyAccessToken(token);
      
      if (decoded) {
        const user = await this.userService.findById(decoded.userId);
        if (user && user.status === 'active') {
          req.user = {
            id: user.id,
            email: user.email,
            name: user.name,
            subscription: user.subscription
          };
        }
      }

      next();

    } catch (error) {
      logger.error('Optional authentication error:', error);
      // Continue without authentication on error
      next();
    }
  };

  /**
   * Middleware to check if user has required subscription features
   */
  public requireFeature = (feature: string) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_REQUIRED',
            message: 'Authentication required'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      const userFeatures = req.user.subscription.features || [];
      if (!userFeatures.includes(feature)) {
        res.status(403).json({
          success: false,
          error: {
            code: 'FEATURE_NOT_AVAILABLE',
            message: `Feature '${feature}' is not available in your subscription plan`
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      next();
    };
  };

  /**
   * Middleware to check subscription plan
   */
  public requirePlan = (plans: string[]) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_REQUIRED',
            message: 'Authentication required'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      const userPlan = req.user.subscription.type;
      if (!plans.includes(userPlan)) {
        res.status(403).json({
          success: false,
          error: {
            code: 'PLAN_UPGRADE_REQUIRED',
            message: `This feature requires one of the following plans: ${plans.join(', ')}`
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      next();
    };
  };

  /**
   * Middleware to check usage limits
   */
  public checkUsageLimit = (limitType: string) => {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        if (!req.user) {
          res.status(401).json({
            success: false,
            error: {
              code: 'AUTHENTICATION_REQUIRED',
              message: 'Authentication required'
            },
            timestamp: new Date().toISOString()
          } as ApiResponse);
          return;
        }

        const userLimits = req.user.subscription.limits;
        const limit = userLimits[limitType];

        // -1 means unlimited
        if (limit === -1) {
          next();
          return;
        }

        // Check current usage (this would typically query the database)
        const currentUsage = await this.getCurrentUsage(req.user.id, limitType);
        
        if (currentUsage >= limit) {
          res.status(429).json({
            success: false,
            error: {
              code: 'USAGE_LIMIT_EXCEEDED',
              message: `You have exceeded your ${limitType} limit of ${limit}`
            },
            timestamp: new Date().toISOString()
          } as ApiResponse);
          return;
        }

        next();

      } catch (error) {
        logger.error('Usage limit check error:', error);
        next(error);
      }
    };
  };

  /**
   * Middleware for admin-only routes
   */
  public requireAdmin = (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication required'
        },
        timestamp: new Date().toISOString()
      } as ApiResponse);
      return;
    }

    // Check if user has admin role (this would be stored in user object)
    const isAdmin = req.user.subscription.features.includes('admin_access');
    if (!isAdmin) {
      res.status(403).json({
        success: false,
        error: {
          code: 'ADMIN_ACCESS_REQUIRED',
          message: 'Administrator access required'
        },
        timestamp: new Date().toISOString()
      } as ApiResponse);
      return;
    }

    next();
  };

  /**
   * Middleware to validate API key for external integrations
   */
  public validateApiKey = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const apiKey = req.headers['x-api-key'] as string;
      
      if (!apiKey) {
        res.status(401).json({
          success: false,
          error: {
            code: 'API_KEY_REQUIRED',
            message: 'API key is required'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      // Validate API key (this would typically check against database)
      const isValidKey = await this.validateApiKeyInDatabase(apiKey);
      if (!isValidKey) {
        res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_API_KEY',
            message: 'Invalid API key'
          },
          timestamp: new Date().toISOString()
        } as ApiResponse);
        return;
      }

      next();

    } catch (error) {
      logger.error('API key validation error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'API_KEY_VALIDATION_ERROR',
          message: 'API key validation failed'
        },
        timestamp: new Date().toISOString()
      } as ApiResponse);
    }
  };

  /**
   * Get current usage for a user and limit type
   */
  private async getCurrentUsage(userId: string, limitType: string): Promise<number> {
    // This would typically query the database to get current usage
    // For now, return 0 as placeholder
    try {
      switch (limitType) {
        case 'monthlyMinutes':
          // Query audio processing usage for current month
          return 0;
        case 'storageGB':
          // Query total storage usage
          return 0;
        case 'collaborators':
          // Query number of collaborators
          return 0;
        default:
          return 0;
      }
    } catch (error) {
      logger.error(`Error getting usage for ${limitType}:`, error);
      return 0;
    }
  }

  /**
   * Validate API key against database
   */
  private async validateApiKeyInDatabase(apiKey: string): Promise<boolean> {
    // This would typically query the database to validate the API key
    // For now, return false as placeholder
    try {
      // Implementation would go here
      return false;
    } catch (error) {
      logger.error('Error validating API key:', error);
      return false;
    }
  }
}

// Create singleton instance
const authMiddleware = new AuthMiddleware();

// Export middleware functions
export const authMiddleware as authMiddleware = authMiddleware.authenticate;
export const optionalAuth = authMiddleware.optionalAuth;
export const requireFeature = authMiddleware.requireFeature;
export const requirePlan = authMiddleware.requirePlan;
export const checkUsageLimit = authMiddleware.checkUsageLimit;
export const requireAdmin = authMiddleware.requireAdmin;
export const validateApiKey = authMiddleware.validateApiKey;

export default authMiddleware;
