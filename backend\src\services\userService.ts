import { PrismaClient, User, UserStatus, SubscriptionType } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { logger } from '../utils/logger';
import { NotFoundError, ValidationError, ConflictError } from '../middleware/errorHandler';

const prisma = new PrismaClient();

export interface CreateUserData {
  email: string;
  name: string;
  password: string;
  avatar?: string;
}

export interface UpdateUserData {
  name?: string;
  avatar?: string;
  preferences?: any;
}

export interface UserWithoutPassword extends Omit<User, 'password'> {}

export class UserService {
  
  /**
   * Create a new user
   */
  async createUser(data: CreateUserData): Promise<UserWithoutPassword> {
    try {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: data.email }
      });

      if (existingUser) {
        throw new ConflictError('User with this email already exists');
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(data.password, 12);

      // Create user
      const user = await prisma.user.create({
        data: {
          email: data.email,
          name: data.name,
          password: hashedPassword,
          avatar: data.avatar,
          status: 'ACTIVE',
          subscriptionType: 'FREE',
          subscriptionStatus: 'ACTIVE',
          preferences: {},
          emailVerifiedAt: null
        }
      });

      logger.info('User created', {
        userId: user.id,
        email: user.email,
        name: user.name
      });

      // Return user without password
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      logger.error('Failed to create user', {
        email: data.email,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { email }
      });

      return user;
    } catch (error) {
      logger.error('Failed to find user by email', {
        email,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Find user by ID
   */
  async findById(id: string): Promise<User | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { id }
      });

      return user;
    } catch (error) {
      logger.error('Failed to find user by ID', {
        userId: id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get user profile without password
   */
  async getUserProfile(id: string): Promise<UserWithoutPassword> {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              notes: true,
              audioFiles: true
            }
          }
        }
      });

      if (!user) {
        throw new NotFoundError('User not found');
      }

      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword as UserWithoutPassword;
    } catch (error) {
      logger.error('Failed to get user profile', {
        userId: id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateUser(id: string, data: UpdateUserData): Promise<UserWithoutPassword> {
    try {
      const user = await prisma.user.update({
        where: { id },
        data: {
          ...data,
          updatedAt: new Date()
        }
      });

      logger.info('User updated', {
        userId: id,
        changes: Object.keys(data)
      });

      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundError('User not found');
      }
      logger.error('Failed to update user', {
        userId: id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Update user password
   */
  async updatePassword(id: string, currentPassword: string, newPassword: string): Promise<void> {
    try {
      const user = await prisma.user.findUnique({
        where: { id }
      });

      if (!user) {
        throw new NotFoundError('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        throw new ValidationError('Current password is incorrect');
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(newPassword, 12);

      // Update password
      await prisma.user.update({
        where: { id },
        data: { password: hashedNewPassword }
      });

      logger.info('User password updated', { userId: id });
    } catch (error) {
      logger.error('Failed to update password', {
        userId: id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Verify user password
   */
  async verifyPassword(email: string, password: string): Promise<User | null> {
    try {
      const user = await this.findByEmail(email);
      
      if (!user) {
        return null;
      }

      const isPasswordValid = await bcrypt.compare(password, user.password);
      
      if (!isPasswordValid) {
        return null;
      }

      return user;
    } catch (error) {
      logger.error('Failed to verify password', {
        email,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Update last login timestamp
   */
  async updateLastLogin(id: string): Promise<void> {
    try {
      await prisma.user.update({
        where: { id },
        data: { lastLoginAt: new Date() }
      });
    } catch (error) {
      logger.error('Failed to update last login', {
        userId: id,
        error: error.message
      });
      // Don't throw error for this non-critical operation
    }
  }

  /**
   * Update user status
   */
  async updateUserStatus(id: string, status: UserStatus): Promise<void> {
    try {
      await prisma.user.update({
        where: { id },
        data: { status }
      });

      logger.info('User status updated', {
        userId: id,
        status
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundError('User not found');
      }
      logger.error('Failed to update user status', {
        userId: id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Update user subscription
   */
  async updateSubscription(
    id: string,
    subscriptionType: SubscriptionType,
    expiresAt?: Date
  ): Promise<void> {
    try {
      await prisma.user.update({
        where: { id },
        data: {
          subscriptionType,
          subscriptionExpiresAt: expiresAt,
          subscriptionStatus: 'ACTIVE'
        }
      });

      logger.info('User subscription updated', {
        userId: id,
        subscriptionType,
        expiresAt
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundError('User not found');
      }
      logger.error('Failed to update subscription', {
        userId: id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get user usage statistics
   */
  async getUserUsageStats(id: string): Promise<{
    totalNotes: number;
    totalAudioFiles: number;
    totalTranscriptions: number;
    storageUsed: number;
    monthlyUploads: number;
  }> {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      const [
        totalNotes,
        totalAudioFiles,
        totalTranscriptions,
        audioFiles,
        monthlyUploads
      ] = await Promise.all([
        prisma.note.count({ where: { userId: id } }),
        prisma.audioFile.count({ where: { userId: id } }),
        prisma.transcription.count({
          where: { audioFile: { userId: id } }
        }),
        prisma.audioFile.findMany({
          where: { userId: id },
          select: { size: true }
        }),
        prisma.audioFile.count({
          where: {
            userId: id,
            createdAt: { gte: thirtyDaysAgo }
          }
        })
      ]);

      const storageUsed = audioFiles.reduce((total, file) => total + file.size, 0);

      return {
        totalNotes,
        totalAudioFiles,
        totalTranscriptions,
        storageUsed,
        monthlyUploads
      };
    } catch (error) {
      logger.error('Failed to get user usage stats', {
        userId: id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Delete user account
   */
  async deleteUser(id: string): Promise<void> {
    try {
      // This will cascade delete all related records
      await prisma.user.delete({
        where: { id }
      });

      logger.info('User deleted', { userId: id });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundError('User not found');
      }
      logger.error('Failed to delete user', {
        userId: id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Verify email address
   */
  async verifyEmail(id: string): Promise<void> {
    try {
      await prisma.user.update({
        where: { id },
        data: { emailVerifiedAt: new Date() }
      });

      logger.info('Email verified', { userId: id });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundError('User not found');
      }
      logger.error('Failed to verify email', {
        userId: id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Check if user has reached subscription limits
   */
  async checkSubscriptionLimits(id: string): Promise<{
    canUploadAudio: boolean;
    canCreateNote: boolean;
    remainingUploads: number;
    remainingStorage: number;
  }> {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
        select: { subscriptionType: true }
      });

      if (!user) {
        throw new NotFoundError('User not found');
      }

      const stats = await this.getUserUsageStats(id);

      // Define limits based on subscription type
      const limits = {
        FREE: { uploads: 10, storage: 100 * 1024 * 1024 }, // 100MB
        PREMIUM: { uploads: 100, storage: 1024 * 1024 * 1024 }, // 1GB
        ENTERPRISE: { uploads: -1, storage: -1 } // Unlimited
      };

      const userLimits = limits[user.subscriptionType];
      
      const canUploadAudio = userLimits.uploads === -1 || stats.monthlyUploads < userLimits.uploads;
      const canCreateNote = true; // No limits on notes for now
      const remainingUploads = userLimits.uploads === -1 ? -1 : Math.max(0, userLimits.uploads - stats.monthlyUploads);
      const remainingStorage = userLimits.storage === -1 ? -1 : Math.max(0, userLimits.storage - stats.storageUsed);

      return {
        canUploadAudio: canUploadAudio && (userLimits.storage === -1 || stats.storageUsed < userLimits.storage),
        canCreateNote,
        remainingUploads,
        remainingStorage
      };
    } catch (error) {
      logger.error('Failed to check subscription limits', {
        userId: id,
        error: error.message
      });
      throw error;
    }
  }
}

export default new UserService();
