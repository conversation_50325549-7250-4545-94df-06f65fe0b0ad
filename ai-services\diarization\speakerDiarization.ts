import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';

export interface SpeakerSegment {
  speakerId: string;
  start: number;
  end: number;
  confidence: number;
  text?: string;
}

export interface DiarizationResult {
  segments: SpeakerSegment[];
  speakers: Array<{
    id: string;
    totalDuration: number;
    segmentCount: number;
    averageConfidence: number;
  }>;
  totalSpeakers: number;
  processingTime: number;
  confidence: number;
}

export class SpeakerDiarizationService {
  private tempDir: string;

  constructor() {
    this.tempDir = path.join(process.cwd(), 'temp', 'diarization');
    this.ensureTempDir();
  }

  /**
   * Perform speaker diarization on audio file
   */
  async diarizeAudio(audioFilePath: string, options?: {
    minSpeakers?: number;
    maxSpeakers?: number;
    language?: string;
  }): Promise<DiarizationResult> {
    const startTime = Date.now();
    const sessionId = uuidv4();
    
    try {
      logger.info('Starting speaker diarization', {
        sessionId,
        audioFilePath,
        options
      });

      // Validate audio file
      if (!fs.existsSync(audioFilePath)) {
        throw new Error(`Audio file not found: ${audioFilePath}`);
      }

      // Convert audio to required format if needed
      const processedAudioPath = await this.preprocessAudio(audioFilePath, sessionId);

      // Perform diarization using pyannote.audio (Python-based)
      const diarizationResult = await this.runPyannoteAudio(processedAudioPath, options);

      // Clean up temporary files
      await this.cleanup(sessionId);

      const processingTime = Date.now() - startTime;
      
      const result: DiarizationResult = {
        ...diarizationResult,
        processingTime
      };

      logger.info('Speaker diarization completed', {
        sessionId,
        processingTime,
        totalSpeakers: result.totalSpeakers,
        segmentCount: result.segments.length,
        confidence: result.confidence
      });

      return result;
    } catch (error) {
      await this.cleanup(sessionId);
      const processingTime = Date.now() - startTime;
      
      logger.error('Speaker diarization failed', {
        sessionId,
        error: error.message,
        processingTime,
        audioFilePath
      });
      
      throw error;
    }
  }

  /**
   * Combine diarization results with transcription
   */
  async combineWithTranscription(
    diarizationResult: DiarizationResult,
    transcriptionSegments: Array<{
      start: number;
      end: number;
      text: string;
    }>
  ): Promise<Array<{
    speakerId: string;
    start: number;
    end: number;
    text: string;
    confidence: number;
  }>> {
    try {
      logger.info('Combining diarization with transcription', {
        diarizationSegments: diarizationResult.segments.length,
        transcriptionSegments: transcriptionSegments.length
      });

      const combinedSegments = [];

      for (const diarSegment of diarizationResult.segments) {
        // Find overlapping transcription segments
        const overlappingTranscriptions = transcriptionSegments.filter(transSegment => 
          this.segmentsOverlap(diarSegment, transSegment)
        );

        if (overlappingTranscriptions.length > 0) {
          // Combine text from overlapping segments
          const combinedText = overlappingTranscriptions
            .map(seg => seg.text)
            .join(' ')
            .trim();

          combinedSegments.push({
            speakerId: diarSegment.speakerId,
            start: diarSegment.start,
            end: diarSegment.end,
            text: combinedText,
            confidence: diarSegment.confidence
          });
        } else {
          // No transcription found for this speaker segment
          combinedSegments.push({
            speakerId: diarSegment.speakerId,
            start: diarSegment.start,
            end: diarSegment.end,
            text: '[No transcription available]',
            confidence: diarSegment.confidence * 0.5 // Reduce confidence
          });
        }
      }

      logger.info('Diarization and transcription combined', {
        combinedSegments: combinedSegments.length
      });

      return combinedSegments;
    } catch (error) {
      logger.error('Failed to combine diarization with transcription', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Identify speakers using voice embeddings (simplified version)
   */
  async identifySpeakers(
    diarizationResult: DiarizationResult,
    knownSpeakers?: Array<{
      id: string;
      name: string;
      voiceProfile?: any; // Voice embedding data
    }>
  ): Promise<DiarizationResult> {
    try {
      if (!knownSpeakers || knownSpeakers.length === 0) {
        return diarizationResult;
      }

      logger.info('Identifying speakers', {
        totalSpeakers: diarizationResult.totalSpeakers,
        knownSpeakers: knownSpeakers.length
      });

      // This is a simplified implementation
      // In a real-world scenario, you would use voice embeddings and similarity matching
      const identifiedSpeakers = diarizationResult.speakers.map((speaker, index) => {
        if (index < knownSpeakers.length) {
          return {
            ...speaker,
            id: knownSpeakers[index].id,
            name: knownSpeakers[index].name
          };
        }
        return speaker;
      });

      const result = {
        ...diarizationResult,
        speakers: identifiedSpeakers
      };

      logger.info('Speaker identification completed', {
        identifiedSpeakers: identifiedSpeakers.filter(s => s.name).length
      });

      return result;
    } catch (error) {
      logger.error('Speaker identification failed', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Preprocess audio for diarization
   */
  private async preprocessAudio(audioFilePath: string, sessionId: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const outputPath = path.join(this.tempDir, `${sessionId}_processed.wav`);
      
      // Convert to mono WAV at 16kHz for optimal diarization
      const ffmpeg = spawn('ffmpeg', [
        '-i', audioFilePath,
        '-ac', '1', // Mono
        '-ar', '16000', // 16kHz sample rate
        '-f', 'wav',
        '-y', // Overwrite output file
        outputPath
      ]);

      ffmpeg.stderr.on('data', (data) => {
        logger.debug('FFmpeg preprocessing', { data: data.toString() });
      });

      ffmpeg.on('close', (code) => {
        if (code === 0) {
          resolve(outputPath);
        } else {
          reject(new Error(`Audio preprocessing failed with code ${code}`));
        }
      });

      ffmpeg.on('error', (error) => {
        reject(new Error(`FFmpeg error: ${error.message}`));
      });
    });
  }

  /**
   * Run pyannote.audio for speaker diarization
   */
  private async runPyannoteAudio(
    audioFilePath: string,
    options?: {
      minSpeakers?: number;
      maxSpeakers?: number;
    }
  ): Promise<Omit<DiarizationResult, 'processingTime'>> {
    return new Promise((resolve, reject) => {
      // This is a simplified implementation
      // In production, you would use the actual pyannote.audio Python library
      
      // For now, we'll simulate the diarization process
      setTimeout(() => {
        try {
          // Simulate diarization results
          const mockResult = this.generateMockDiarizationResult(options);
          resolve(mockResult);
        } catch (error) {
          reject(error);
        }
      }, 2000); // Simulate processing time
    });
  }

  /**
   * Generate mock diarization result for demonstration
   */
  private generateMockDiarizationResult(options?: {
    minSpeakers?: number;
    maxSpeakers?: number;
  }): Omit<DiarizationResult, 'processingTime'> {
    const numSpeakers = Math.max(
      options?.minSpeakers || 2,
      Math.min(options?.maxSpeakers || 4, Math.floor(Math.random() * 3) + 2)
    );

    const segments: SpeakerSegment[] = [];
    const speakers = [];

    // Generate mock segments
    let currentTime = 0;
    for (let i = 0; i < 20; i++) {
      const speakerId = `speaker_${(i % numSpeakers) + 1}`;
      const duration = Math.random() * 10 + 2; // 2-12 seconds
      
      segments.push({
        speakerId,
        start: currentTime,
        end: currentTime + duration,
        confidence: 0.8 + Math.random() * 0.2
      });

      currentTime += duration + Math.random() * 2; // Add pause
    }

    // Generate speaker statistics
    for (let i = 1; i <= numSpeakers; i++) {
      const speakerId = `speaker_${i}`;
      const speakerSegments = segments.filter(s => s.speakerId === speakerId);
      const totalDuration = speakerSegments.reduce((sum, seg) => sum + (seg.end - seg.start), 0);
      const averageConfidence = speakerSegments.reduce((sum, seg) => sum + seg.confidence, 0) / speakerSegments.length;

      speakers.push({
        id: speakerId,
        totalDuration,
        segmentCount: speakerSegments.length,
        averageConfidence
      });
    }

    return {
      segments,
      speakers,
      totalSpeakers: numSpeakers,
      confidence: 0.85
    };
  }

  /**
   * Check if two segments overlap
   */
  private segmentsOverlap(
    seg1: { start: number; end: number },
    seg2: { start: number; end: number }
  ): boolean {
    return seg1.start < seg2.end && seg2.start < seg1.end;
  }

  /**
   * Ensure temporary directory exists
   */
  private ensureTempDir(): void {
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  /**
   * Clean up temporary files
   */
  private async cleanup(sessionId: string): Promise<void> {
    try {
      const files = fs.readdirSync(this.tempDir);
      const sessionFiles = files.filter(file => file.includes(sessionId));
      
      for (const file of sessionFiles) {
        const filePath = path.join(this.tempDir, file);
        fs.unlinkSync(filePath);
      }
      
      logger.debug('Cleanup completed', { sessionId, filesRemoved: sessionFiles.length });
    } catch (error) {
      logger.warn('Cleanup failed', { sessionId, error: error.message });
    }
  }
}

export default new SpeakerDiarizationService();
